---
inclusion: always
---

# Kratos 微服务开发指南总览

本文档集合为基于 Go-Kratos 框架的微服务项目提供完整的开发规范和最佳实践指导。

## 文档结构

### 📋 基础规范
1. **[项目概览](01-项目概览.md)** - 项目架构、技术栈、开发流程
2. **[代码规范](02-代码规范.md)** - 分层架构、命名规范、错误处理

### 🔧 开发规范  
3. **[API设计规范](03-API设计规范.md)** - Proto文件、HTTP路由、错误码设计
4. **[数据库设计规范](04-数据库设计规范.md)** - Ent ORM、索引设计、事务管理
5. **[业务逻辑规范](05-业务逻辑规范.md)** - Domain层、Biz层、Service层实现

### ⚙️ 配置与运维
6. **[配置管理规范](06-配置管理规范.md)** - 配置文件、环境变量、多环境管理
7. **[测试规范](07-测试规范.md)** - 单元测试、集成测试、E2E测试
8. **[部署运维规范](08-部署运维规范.md)** - Docker、Kubernetes、CI/CD

### 🚀 性能与安全
9. **[性能优化规范](09-性能优化规范.md)** - 数据库优化、缓存策略、并发控制
10. **[安全规范](10-安全规范.md)** - 身份认证、数据加密、安全审计

## 快速开始

### 新项目创建流程
```bash
# 1. 创建项目
kratos new myapp
cd myapp

# 2. 定义 API
# 编辑 api/user/v1/user.proto

# 3. 生成代码
make api

# 4. 实现业务逻辑
# 按照分层架构实现 Domain -> Biz -> Service -> Data

# 5. 配置依赖注入
make wire

# 6. 启动服务
kratos run
```

### 开发检查清单

#### API 设计 ✅
- [ ] Proto 文件按服务和版本组织
- [ ] 定义完整的错误码
- [ ] 添加参数验证规则
- [ ] 生成 OpenAPI 文档

#### 代码实现 ✅
- [ ] Domain 层定义实体和接口
- [ ] Biz 层实现业务逻辑
- [ ] Service 层处理 DTO 转换
- [ ] Data 层实现数据访问
- [ ] 正确的错误处理和日志记录

#### 数据库设计 ✅
- [ ] 合理的表结构和索引
- [ ] 多租户支持
- [ ] 软删除机制
- [ ] 数据迁移脚本

#### 测试覆盖 ✅
- [ ] 单元测试覆盖率 > 80%
- [ ] 关键业务流程集成测试
- [ ] API 端到端测试
- [ ] 性能基准测试

#### 安全防护 ✅
- [ ] JWT 身份认证
- [ ] RBAC 权限控制
- [ ] 输入参数验证
- [ ] 敏感数据加密
- [ ] 安全审计日志

#### 部署准备 ✅
- [ ] Docker 镜像构建
- [ ] Kubernetes 部署清单
- [ ] 监控告警配置
- [ ] 日志收集配置
- [ ] 备份恢复方案

## 常用命令速查

### 开发命令
```bash
# 生成 API 代码
make api

# 生成 ORM 代码  
make ent

# 生成依赖注入代码
make wire

# 生成所有代码
make all

# 运行测试
make test

# 启动服务
kratos run
```

### 构建部署
```bash
# 构建二进制
make build

# 构建 Docker 镜像
docker build -t myapp:latest .

# 启动开发环境
docker-compose up -d

# 部署到 Kubernetes
kubectl apply -f deploy/kubernetes/
```

## 项目结构参考

```
myapp/
├── api/                    # API 定义
│   └── user/v1/
│       ├── user.proto
│       └── error_reason.proto
├── cmd/server/             # 应用入口
│   ├── main.go
│   ├── wire.go
│   └── wire_gen.go
├── configs/                # 配置文件
│   ├── config.yaml
│   └── config.prod.yaml
├── internal/               # 内部代码
│   ├── biz/               # 业务逻辑层
│   ├── data/              # 数据访问层
│   ├── domain/            # 领域模型层
│   ├── service/           # 服务接口层
│   ├── server/            # 服务器配置
│   └── gen/               # 生成代码
├── deploy/                 # 部署配置
│   ├── docker/
│   └── kubernetes/
├── scripts/                # 脚本文件
├── test/                   # 测试文件
├── third_party/            # 第三方依赖
├── Dockerfile
├── Makefile
└── README.md
```

## 最佳实践要点

### 架构设计
- 严格遵循分层架构，避免跨层调用
- 使用依赖倒置原则，面向接口编程
- 合理划分领域边界，避免贫血模型

### 性能优化
- 数据库查询优化，避免 N+1 问题
- 合理使用缓存，多级缓存策略
- 连接池配置优化
- 异步处理非关键业务

### 安全防护
- 所有外部输入必须验证
- 敏感数据加密存储
- 完善的身份认证和授权
- 详细的安全审计日志

### 运维监控
- 完善的健康检查机制
- 详细的业务和技术指标监控
- 分布式链路追踪
- 自动化部署和回滚

## 技术支持

### 相关资源
- [Go-Kratos 官方文档](https://go-kratos.dev/)
- [Ent ORM 文档](https://entgo.io/)
- [Protocol Buffers 指南](https://developers.google.com/protocol-buffers)
- [Kubernetes 文档](https://kubernetes.io/docs/)

### 问题排查
1. 查看应用日志: `kubectl logs -f deployment/myapp`
2. 检查服务状态: `kubectl get pods,svc`
3. 查看配置: `kubectl describe configmap myapp-config`
4. 性能分析: 访问 `/debug/pprof/` 端点

---

**注意**: 本指南会根据项目发展和最佳实践的演进持续更新，请定期查看最新版本。