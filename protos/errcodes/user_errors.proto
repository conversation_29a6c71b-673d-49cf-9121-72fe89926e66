syntax = "proto3";

package errcodes;

import "errors/errors.proto";

option go_package = "github.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodes";

// UserErrorReason 定义用户服务相关错误码
enum UserErrorReason {
  // 设置错误码前缀
  option (errors.default_code) = 400;

  // 用户未找到
  USER_NOT_FOUND = 0 [(errors.code) = 404, (errors.message) = "用户不存在"];
  
  // 用户已存在
  USER_ALREADY_EXISTS = 1 [(errors.code) = 409, (errors.message) = "用户已存在"];
  
  // 无效的用户参数
  INVALID_USER_PARAMS = 2 [(errors.code) = 400, (errors.message) = "无效的用户参数"];
  
  // 租户ID无效
  INVALID_TENANT_ID = 3 [(errors.code) = 400, (errors.message) = "无效的租户ID"];
  
  // 用户名已被使用
  USERNAME_ALREADY_TAKEN = 4 [(errors.code) = 409, (errors.message) = "用户名已被使用"];
  
  // 年龄超出范围
  AGE_OUT_OF_RANGE = 5 [(errors.code) = 400, (errors.message) = "年龄超出有效范围"];

  // 业务规则违反
  BUSINESS_RULE_VIOLATION = 6 [(errors.code) = 422, (errors.message) = "业务规则违反"];

  // 用户邮箱已存在
  USER_EMAIL_EXISTS = 7 [(errors.code) = 409, (errors.message) = "邮箱已被使用"];

  // 用户手机号已存在
  USER_PHONE_EXISTS = 8 [(errors.code) = 409, (errors.message) = "手机号已被使用"];

  // 权限被拒绝
  PERMISSION_DENIED = 9 [(errors.code) = 403, (errors.message) = "权限不足"];
}