syntax = "proto3";

package errcodes;

import "errors/errors.proto";

option go_package = "github.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodes";

// CommonErrorReason 定义通用错误码
enum CommonErrorReason {
  // 设置错误码前缀
  option (errors.default_code) = 500;

  // 未指定错误
  ERROR_REASON_UNSPECIFIED = 0 [(errors.code) = 500, (errors.message) = "未知错误"];
  
  // 系统错误
  SYSTEM_ERROR = 1 [(errors.code) = 500, (errors.message) = "系统内部错误"];
  
  // 参数验证错误
  INVALID_PARAMETER = 4 [(errors.code) = 400, (errors.message) = "无效的参数"];
}