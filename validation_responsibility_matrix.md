# 参数校验职责边界说明文档

## 概述

本文档明确了各层级在参数校验中的职责边界，确保校验逻辑的合理分层和职责分离。

## 各层职责说明

### 1. Transport 层（Service 层）
**职责范围：** 协议/格式校验

- **主要职责：** 验证请求数据的协议格式正确性
- **校验内容：**
  - HTTP 请求格式校验
  - JSON/XML 格式校验
  - 参数类型校验（string、int、float 等）
  - 必填字段检查
  - 字段长度限制
  - 数据格式规范（如邮箱格式、手机号格式）
- **实现方式：** protobuf-validate + 拦截器
- **校验时机：** 请求进入系统的第一时间
- **失败处理：** 直接返回 400 Bad Request

**示例：**
```proto
message CreateUserRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string email = 2 [(validate.rules).string.pattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"];
  int32 age = 3 [(validate.rules).int32.gte = 0];
}
```

### 2. Domain 层（Entity.Validate）
**职责范围：** 领域不变量校验

- **主要职责：** 维护领域模型的完整性和一致性
- **校验内容：**
  - 业务规则约束（如年龄 ≥ 0）
  - 领域对象状态一致性
  - 单个实体内部约束
  - 领域特定的业务规则
- **实现方式：** 实体内部的 Validate() 方法
- **校验时机：** 创建或修改领域对象时
- **失败处理：** 返回领域错误

**示例：**
```go
type User struct {
    ID    int64
    Name  string
    Email string
    Age   int
}

func (u *User) Validate() error {
    if u.Name == "" {
        return errors.New("用户名不能为空")
    }
    if u.Age < 0 {
        return errors.New("年龄不能为负数")
    }
    if u.Age > 150 {
        return errors.New("年龄不能超过150岁")
    }
    return nil
}
```

### 3. Usecase 层（应用服务层）
**职责范围：** 依赖外部资源的组合校验

- **主要职责：** 协调多个领域对象和外部依赖的复合校验
- **校验内容：**
  - 跨实体关系校验
  - 外部资源存在性验证（如租户存在性）
  - 权限校验
  - 业务流程状态校验
  - 数据唯一性校验
- **实现方式：** 应用服务方法中的校验逻辑
- **校验时机：** 执行业务操作前
- **失败处理：** 返回业务错误

**示例：**
```go
func (uc *UserUseCase) CreateUser(ctx context.Context, req CreateUserRequest) error {
    // 1. 检查租户是否存在
    tenant, err := uc.tenantRepo.GetByID(ctx, req.TenantID)
    if err != nil {
        return errors.New("租户不存在")
    }
    
    // 2. 检查邮箱唯一性
    exists, err := uc.userRepo.ExistsByEmail(ctx, req.Email)
    if err != nil {
        return err
    }
    if exists {
        return errors.New("邮箱已存在")
    }
    
    // 3. 检查用户权限
    if !uc.authService.HasPermission(ctx, "create_user") {
        return errors.New("无权限创建用户")
    }
    
    // 4. 创建用户实体并校验
    user := &User{...}
    if err := user.Validate(); err != nil {
        return err
    }
    
    // 5. 保存用户
    return uc.userRepo.Save(ctx, user)
}
```

## 层级 × 校验类型矩阵

| 校验类型 / 层级 | Transport 层 | Domain 层 | Usecase 层 |
|----------------|-------------|-----------|------------|
| **格式校验** | ✅ 主要职责<br/>• JSON/XML格式<br/>• 数据类型<br/>• 字段格式 | ❌ 不负责 | ❌ 不负责 |
| **必填字段校验** | ✅ 主要职责<br/>• 协议级必填字段 | ✅ 辅助职责<br/>• 业务级必填字段 | ❌ 不负责 |
| **数据类型校验** | ✅ 主要职责<br/>• 基础类型转换<br/>• 枚举值校验 | ❌ 不负责 | ❌ 不负责 |
| **长度/范围校验** | ✅ 主要职责<br/>• 字符串长度<br/>• 数值范围 | ✅ 辅助职责<br/>• 业务语义范围 | ❌ 不负责 |
| **领域不变量** | ❌ 不负责 | ✅ 主要职责<br/>• 年龄 ≥ 0<br/>• 状态转换规则<br/>• 实体完整性 | ❌ 不负责 |
| **业务规则校验** | ❌ 不负责 | ✅ 主要职责<br/>• 单实体业务规则<br/>• 领域约束 | ✅ 辅助职责<br/>• 跨实体业务规则 |
| **唯一性校验** | ❌ 不负责 | ❌ 不负责 | ✅ 主要职责<br/>• 数据库唯一性<br/>• 跨表唯一性 |
| **存在性校验** | ❌ 不负责 | ❌ 不负责 | ✅ 主要职责<br/>• 外键存在性<br/>• 依赖资源存在性 |
| **权限校验** | ❌ 不负责 | ❌ 不负责 | ✅ 主要职责<br/>• 操作权限<br/>• 数据权限 |
| **状态校验** | ❌ 不负责 | ✅ 辅助职责<br/>• 实体状态一致性 | ✅ 主要职责<br/>• 业务流程状态<br/>• 跨实体状态 |
| **组合校验** | ❌ 不负责 | ❌ 不负责 | ✅ 主要职责<br/>• 多字段组合逻辑<br/>• 跨实体关系 |

## 校验失败处理策略

### Transport 层
- **错误类型：** 客户端错误
- **HTTP 状态码：** 400 Bad Request
- **错误格式：** 标准化错误响应
- **处理方式：** 立即返回，不进入业务逻辑

### Domain 层
- **错误类型：** 领域错误
- **错误处理：** 抛出领域异常
- **错误信息：** 业务友好的错误描述
- **处理方式：** 由上层捕获并转换

### Usecase 层
- **错误类型：** 业务错误
- **错误处理：** 返回业务异常
- **错误信息：** 具体的业务错误描述
- **处理方式：** 转换为适当的 HTTP 状态码

## 最佳实践

1. **单一职责原则：** 每层只负责自己职责范围内的校验
2. **快速失败：** 在最早可能的层级进行校验
3. **错误信息明确：** 提供清晰的错误描述和修复建议
4. **性能考虑：** 轻量级校验在前，重量级校验在后
5. **可维护性：** 校验逻辑与业务逻辑分离

## 总结

通过明确的职责分工，我们可以构建一个清晰、可维护的多层校验体系：

- **Transport 层** 专注于数据格式和协议校验
- **Domain 层** 维护领域模型的完整性
- **Usecase 层** 处理复杂的业务逻辑校验

这种分层设计确保了代码的清晰性、可测试性和可维护性。
