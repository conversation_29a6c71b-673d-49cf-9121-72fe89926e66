# Proto Validate 最佳实践规范

## 概述

本文档旨在为使用 Protocol Buffers 验证（buf.validate）提供标准化的最佳实践指南，确保代码的一致性、可维护性和可扩展性。

## 1. 基础验证规则使用

### 1.1 必填字段规则

使用 `required` 标记必填字段：

```protobuf
message User {
  // 用户名必填
  string username = 1 [(buf.validate.field).string.min_len = 1];
  
  // 邮箱必填且格式验证
  string email = 2 [(buf.validate.field).string = {
    min_len: 1,
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }];
}
```

### 1.2 数值范围验证

使用 `gt`（大于）、`gte`（大于等于）、`lt`（小于）、`lte`（小于等于）：

```protobuf
message UserProfile {
  // 年龄：0-150岁
  int32 age = 1 [(buf.validate.field).int32 = {gt: 0, lte: 150}];
  
  // 评分：1-5分
  float rating = 2 [(buf.validate.field).float = {gte: 1.0, lte: 5.0}];
  
  // 价格：必须为正数
  double price = 3 [(buf.validate.field).double.gt = 0];
}
```

### 1.3 字符串验证

```protobuf
message Product {
  // 产品名称：长度限制
  string name = 1 [(buf.validate.field).string = {
    min_len: 2,
    max_len: 100
  }];
  
  // 产品编码：固定格式
  string code = 2 [(buf.validate.field).string = {
    min_len: 8,
    max_len: 8,
    pattern: "^[A-Z]{2}[0-9]{6}$"
  }];
  
  // 描述：可选，但有长度限制
  string description = 3 [(buf.validate.field).string.max_len = 500];
}
```

### 1.4 集合验证

```protobuf
message Order {
  // 订单项：至少包含一个项目
  repeated OrderItem items = 1 [(buf.validate.field).repeated = {
    min_items: 1,
    max_items: 100
  }];
  
  // 标签：可选，但数量有限制
  repeated string tags = 2 [(buf.validate.field).repeated.max_items = 10];
}
```

## 2. 枚举和正则表达式管理

### 2.1 枚举验证最佳实践

将枚举的验证说明和业务含义放在 message 末尾的注释中：

```protobuf
message UserAccount {
  string username = 1 [(buf.validate.field).string.min_len = 1];
  UserStatus status = 2 [(buf.validate.field).enum.defined_only = true];
  UserRole role = 3 [(buf.validate.field).enum.defined_only = true];
  
  /*
   * 枚举值说明：
   * UserStatus:
   *   - ACTIVE(1): 活跃用户，可正常使用所有功能
   *   - INACTIVE(2): 非活跃用户，功能受限
   *   - SUSPENDED(3): 暂停用户，禁止登录
   *   - DELETED(4): 已删除用户，仅保留数据记录
   * 
   * UserRole:
   *   - GUEST(1): 访客，只读权限
   *   - USER(2): 普通用户，基础操作权限
   *   - ADMIN(3): 管理员，完整管理权限
   *   - SUPER_ADMIN(4): 超级管理员，系统级权限
   */
}

enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_SUSPENDED = 3;
  USER_STATUS_DELETED = 4;
}

enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_GUEST = 1;
  USER_ROLE_USER = 2;
  USER_ROLE_ADMIN = 3;
  USER_ROLE_SUPER_ADMIN = 4;
}
```

### 2.2 正则表达式集中管理

将复杂的正则表达式统一放在 message 末尾注释中，便于维护：

```protobuf
message ContactInfo {
  string phone = 1 [(buf.validate.field).string.pattern = "^\\+?[1-9]\\d{1,14}$"];
  string id_card = 2 [(buf.validate.field).string.pattern = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$"];
  string postal_code = 3 [(buf.validate.field).string.pattern = "^\\d{6}$"];
  
  /*
   * 正则表达式说明：
   * phone: ^\\+?[1-9]\\d{1,14}$ 
   *   - 国际电话号码格式，可选+号开头，1-15位数字
   * 
   * id_card: ^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$
   *   - 中国身份证号码格式（18位）
   *   - 前6位：地区代码
   *   - 接下来8位：出生日期（YYYYMMDD）
   *   - 后4位：顺序码+校验码
   * 
   * postal_code: ^\\d{6}$
   *   - 中国邮政编码格式，6位数字
   */
}
```

## 3. 避免标签与业务强耦合

### 3.1 错误示例（强耦合）

```protobuf
// ❌ 不推荐：字段标签与具体业务逻辑强耦合
message User {
  string name = 1;
  string user_email_for_marketing = 2;  // 标签暴露了具体业务用途
  int32 vip_level_for_discount = 3;     // 标签与折扣逻辑耦合
}
```

### 3.2 正确示例（低耦合）

```protobuf
// ✅ 推荐：标签保持通用性，业务含义通过注释说明
message User {
  string name = 1;
  string email = 2;        // 用户邮箱，可用于营销、通知等多种场景
  int32 membership_level = 3;  // 会员等级，影响折扣、权限等多种业务逻辑
  
  /*
   * 字段业务用途说明：
   * email: 
   *   - 用户身份验证
   *   - 营销邮件发送
   *   - 系统通知推送
   * 
   * membership_level:
   *   - 1: 普通会员（基础折扣）
   *   - 2: 银卡会员（中级折扣+专属客服）
   *   - 3: 金卡会员（高级折扣+优先处理）
   *   - 4: 钻石会员（最高折扣+专属权益）
   */
}
```

### 3.3 使用语义化命名

```protobuf
message OrderInfo {
  // ✅ 语义化命名，不绑定具体实现
  string customer_identifier = 1;  // 客户标识符（可以是ID、邮箱等）
  repeated ProductReference items = 2;  // 产品引用（可以扩展不同的引用方式）
  PaymentMethod payment_info = 3;  // 支付信息（支持多种支付方式扩展）
}
```

## 4. 自定义验证规则扩展

### 4.1 创建自定义验证器

```protobuf
// 扩展 buf.validate 以支持自定义验证
import "buf/validate/validate.proto";

extend buf.validate.FieldConstraints {
  // 自定义中国手机号验证
  bool chinese_mobile = 1001;
  
  // 自定义业务ID格式验证
  string business_id_format = 1002;
  
  // 自定义时间范围验证
  TimeRangeConstraint time_range = 1003;
}

message TimeRangeConstraint {
  string min_time = 1;  // RFC3339 格式
  string max_time = 2;  // RFC3339 格式
  bool allow_past = 3;  // 是否允许过去时间
}
```

### 4.2 使用自定义验证规则

```protobuf
message UserRegistration {
  // 使用自定义中国手机号验证
  string mobile = 1 [(buf.validate.field).chinese_mobile = true];
  
  // 使用自定义业务ID格式
  string business_id = 2 [(buf.validate.field).business_id_format = "COMP-{8}"];
  
  // 使用自定义时间范围验证
  string appointment_time = 3 [(buf.validate.field).time_range = {
    min_time: "2024-01-01T00:00:00Z",
    max_time: "2024-12-31T23:59:59Z",
    allow_past: false
  }];
}
```

### 4.3 组合验证规则

```protobuf
message ComplexValidation {
  // 组合多个验证规则
  string email = 1 [(buf.validate.field).string = {
    min_len: 5,
    max_len: 100,
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }, (buf.validate.field).chinese_mobile = false];  // 明确指定不是手机号
  
  // 条件验证：根据其他字段的值进行验证
  string verification_code = 2 [(buf.validate.field).string = {
    min_len: 4,
    max_len: 6,
    pattern: "^\\d+$"
  }];
}
```

### 4.4 自定义验证器实现示例（Go）

```go
package validation

import (
    "regexp"
    "strings"
    "time"
)

// 中国手机号验证器
func ValidateChineseMobile(mobile string) error {
    pattern := `^1[3-9]\d{9}$`
    matched, err := regexp.MatchString(pattern, mobile)
    if err != nil {
        return err
    }
    if !matched {
        return errors.New("invalid Chinese mobile number format")
    }
    return nil
}

// 业务ID格式验证器
func ValidateBusinessIDFormat(id, format string) error {
    // 解析格式模板，例如 "COMP-{8}" 表示 "COMP-" + 8位数字
    if strings.Contains(format, "{") {
        // 实现格式解析和验证逻辑
        // ...
    }
    return nil
}

// 时间范围验证器
func ValidateTimeRange(timeStr string, constraint *TimeRangeConstraint) error {
    t, err := time.Parse(time.RFC3339, timeStr)
    if err != nil {
        return err
    }
    
    if constraint.MinTime != "" {
        minTime, _ := time.Parse(time.RFC3339, constraint.MinTime)
        if t.Before(minTime) {
            return errors.New("time is before minimum allowed time")
        }
    }
    
    if constraint.MaxTime != "" {
        maxTime, _ := time.Parse(time.RFC3339, constraint.MaxTime)
        if t.After(maxTime) {
            return errors.New("time is after maximum allowed time")
        }
    }
    
    if !constraint.AllowPast && t.Before(time.Now()) {
        return errors.New("past time is not allowed")
    }
    
    return nil
}
```

## 5. 完整示例

```protobuf
syntax = "proto3";

package example.v1;

import "buf/validate/validate.proto";

message UserRegistrationRequest {
  // 基础信息验证
  string username = 1 [(buf.validate.field).string = {
    min_len: 3,
    max_len: 20,
    pattern: "^[a-zA-Z0-9_]+$"
  }];
  
  string email = 2 [(buf.validate.field).string = {
    min_len: 5,
    max_len: 100,
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }];
  
  // 年龄验证
  int32 age = 3 [(buf.validate.field).int32 = {gt: 0, lte: 150}];
  
  // 枚举验证
  UserType user_type = 4 [(buf.validate.field).enum.defined_only = true];
  
  // 数组验证
  repeated string interests = 5 [(buf.validate.field).repeated = {
    min_items: 1,
    max_items: 10
  }];
  
  // 嵌套消息验证
  ContactInfo contact = 6 [(buf.validate.field).required = true];
  
  /*
   * 验证规则说明：
   * 
   * username: ^[a-zA-Z0-9_]+$
   *   - 3-20个字符，只允许字母、数字和下划线
   *   - 用于系统登录标识
   * 
   * email: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$
   *   - 标准邮箱格式验证
   *   - 用于账户验证和通知
   * 
   * UserType 枚举说明：
   *   - INDIVIDUAL(1): 个人用户
   *   - BUSINESS(2): 企业用户
   *   - ORGANIZATION(3): 组织用户
   * 
   * interests: 用户兴趣标签
   *   - 至少选择1个，最多10个
   *   - 用于个性化推荐
   */
}

enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  USER_TYPE_INDIVIDUAL = 1;
  USER_TYPE_BUSINESS = 2;
  USER_TYPE_ORGANIZATION = 3;
}

message ContactInfo {
  string phone = 1 [(buf.validate.field).string.pattern = "^\\+?[1-9]\\d{1,14}$"];
  string address = 2 [(buf.validate.field).string.max_len = 200];
  string city = 3 [(buf.validate.field).string.max_len = 50];
  string country_code = 4 [(buf.validate.field).string = {
    min_len: 2,
    max_len: 2,
    pattern: "^[A-Z]{2}$"
  }];
}
```

## 6. 总结和注意事项

### 6.1 最佳实践要点

1. **一致性**：在整个项目中保持验证规则的一致性
2. **可读性**：使用清晰的字段名和详细的注释
3. **可维护性**：将复杂规则集中管理，便于修改和维护
4. **可扩展性**：设计时考虑未来的扩展需求
5. **性能**：避免过于复杂的正则表达式，影响验证性能

### 6.2 注意事项

- 正则表达式需要转义特殊字符
- 枚举值建议使用 `defined_only = true` 确保类型安全
- 自定义验证器需要在各个语言实现中保持一致
- 验证错误信息应该对用户友好且具有指导性

### 6.3 版本兼容性

在扩展验证规则时，注意：
- 新增验证规则应向后兼容
- 修改现有规则时需要考虑对现有数据的影响
- 使用版本化 API 来管理破坏性更改
