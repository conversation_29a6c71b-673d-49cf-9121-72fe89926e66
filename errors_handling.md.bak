
# ✅ 错误处理使用规范  
## 基于 `protoc-gen-go-kerrors` 的统一错误管理方案

> 一处定义，全局透传，框架自动映射 —— 让 Go-Kratos 项目的错误处理更简单、更一致。

---

## 🔑 核心理念

- **定义在 `.proto`**：所有业务错误集中声明。
- **生成靠工具**：通过 `protoc-gen-go-kerrors` 自动生成 Go 错误代码。
- **各层无脑传**：data → biz → service → handler 层间无需包装或转换。
- **框架自动转**：Kratos 自动将错误映射为 HTTP 状态码与 JSON 响应。

> 📣 **一句话总结：定义在 proto，生成靠插件，各层直传，框架兜底。**

---

## 1. 错误定义（`.proto`）

```protobuf
syntax = "proto3";

package errcoes;
option go_package = "your-project/internal/gen/errcodes;errcodes";

import "errors/errors.proto";

enum UserError {
  option (errors.default_code) = 400; // 默认 HTTP 状态码

  USER_NOT_FOUND        = 0 [(errors.code) = 404, (errors.message) = "用户不存在"];
  // Invalid credentials
  INVALID_CREDENTIALS   = 1 [(errors.code) = 401]; // 注释即消息
  USER_ALREADY_EXISTS   = 2 [(errors.code) = 409]; // 自动生成："user already exists"
}
```

### 💡 消息优先级（三档）
| 优先级 | 来源 | 示例 |
|--------|------|------|
| 🔴 最高 | `(errors.message)` | `"用户不存在"` |
| 🟡 中等 | 行注释 `//` | `// Invalid credentials` → `"Invalid credentials"` |
| 🟢 最低 | 枚举名转小写空格 | `USER_ALREADY_EXISTS` → `"user already exists"` |

> ⚠️ **错误码范围：`1-600`**  
> 推荐：`4xx` 为业务错误，`5xx` 为系统错误

---

## 2. 生成内容与引用方式

运行命令：
```bash
make errors
```

生成文件：`*_kerrors.pb.go`  
例如：`user_error.proto` → `user_error_kerrors.pb.go`

### ✅ 引用规范（统一别名）
```go
import ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes" // 统一使用 ec (error code)
```

### ✅ 三种生成函数类型

| 类型 | 函数签名 | 用途 |
|------|--------|------|
| 判断函数 | `IsUserNotFound(err error) bool` | 类型检查 |
| 格式化创建 | `ErrorUserNotFoundf(format string, args ...)` | 动态消息 |
| 静态变量 | `var ErrorUserNotFound = ...` | 直接返回标准错误 |

---

## 3. 使用方式（分层实践）

### ✅ 数据层：包装底层错误
```go
func (r *UserRepo) FindByID(id int64) (*User, error) {
    err := r.db.QueryRow(...).Scan(&u)
    if errors.Is(err, sql.ErrNoRows) {
        return nil, ec.ErrorUserNotFound.WithCause(err)
    }
    return nil, ec.ErrorDatabaseError.WithCause(err)
}
```

### ✅ 业务层：逻辑判断 + 透传
```go
func (s *UserService) UpdateEmail(id int64, email string) error {
    user, err := s.repo.FindByID(id)
    if err != nil {
        return err // ❌ 不要 wrap，直接透传
    }

    exists, _ := s.repo.ExistsByEmail(email)
    if exists {
        return ec.ErrorEmailAlreadyInUse.WithMetadata(map[string]string{
            "email": email,
        })
    }

    user.Email = email
    return s.repo.Save(user) // ❌ 同样透传
}
```

### ✅ 接口层：零处理，交给框架
```go
func (h *UserHandler) Get(ctx context.Context, req *pb.GetUserReq) (*pb.UserReply, error) {
    user, err := h.service.GetUser(req.Id)
    if err != nil {
        return nil, err // ✅ 直接返回，Kratos 自动转成 JSON + HTTP Code
    }
    return convert(user), nil
}
```

### ✅ 中间件：也可抛出业务错误
```go
func AuthMiddleware() middleware.Middleware {
    return func(handler middleware.Handler) middleware.Handler {
        return func(ctx context.Context, req interface{}) (interface{}, error) {
            if !validToken(ctx) {
                return nil, ec.ErrorUnauthorized // ✅ 允许
            }
            return handler(ctx, req)
        }
    }
}
```

---

## 4. 高级特性

### 🔗 `WithCause(err error)`：保留原始错误链
```go
return ec.ErrorDatabaseError.WithCause(err)
```
日志中可通过 `errors.FromError(err).Unwrap()` 追踪根源。

### 🏷️ `WithMetadata(map[string]string)`：附加结构化信息
```go
return ec.ErrorInsufficientStock.WithMetadata(map[string]string{
    "product_id": "P123",
    "requested":  "5",
    "available":  "2",
})
```
可用于监控告警、前端提示、审计追踪。

---

## 5. 最佳实践

| 场景 | 推荐做法 |
|------|----------|
| 查不到数据 | `ErrorNotFound.WithCause(err)` |
| 参数校验失败 | `ErrorInvalidInputf("年龄不能小于%d", 18)` |
| 权限不足 | `ErrorPermissionDenied` |
| 请求频繁 | `ErrorTooManyRequests.WithMetadata(...)` |
| 第三方调用失败 | `ErrorThirdPartyFailed.WithCause(err)` |

### ✅ 关键优势
- **统一错误源**：所有层都 import 同一个 `ec` 包
- **无脑透传**：无需理解错误类型，直接向上抛
- **框架处理**：Kratos 自动完成状态码映射
- **调试友好**：完整错误链 + metadata 支持
- **维护简单**：集中定义，变更影响清晰

---

## 6. 日志记录建议

### 说明
- **中间件统一处理**：所有返回的错误会由框架中间件统一记录，业务层无需重复打印。
- **仅需透传错误**：直接返回错误即可，避免冗余日志。

使用示例：
```go
err := s.repo.DeleteUser(id)
if err != nil {
    // ❌ 无需手动记录错误，中间件会统一处理
    return err
}
```

---

## 7. 注意事项

- ❗ **错误码唯一性**：避免跨文件重复。
- 🌍 **支持国际化**：建议 reason 固定英文，前端根据 code 显示本地化文案。
- 🔐 **禁止泄露敏感信息**：不得在 message 或 metadata 中暴露密码、token 等。
- ⚖️ **元数据适度添加**：高频接口避免过多 metadata 影响性能。
- 🔀 **向后兼容**：已发布的 error code 和 reason 不建议修改。
- 📊 **性能考虑**: 高频路径避免过多 metadata

---

## 🧩 插件说明（可选附录）

`protoc-gen-go-kerrors` 是一个 Protobuf 插件，用于生成 Kratos 兼容的错误处理代码。

### 安装
```bash
go install gitea.sweet7.com/cmd/protoc-gen-go-kerrors@latest
```

### 生成命令
```bash
protoc \
  --plugin=./protoc-gen-go-kerrors \
  --go-kerrors_out=. --go-kerrors_opt=paths=source_relative:./internal/gen \
  your_error.proto
```

### 目录结构推荐
```
kratos-layout/
├── api/
│   └── user/v1/user_errors.proto
├── proto/errors/
│   └── user_errors.proto  ← 错误定义
└── internal/
    ├── data/   → 使用 ec.ErrorXXX
    ├── biz/    → 使用 ec.ErrorXXX
    └── service/→ 使用 ec.ErrorXXX
    └── domain/→  使用 ec.ErrorXXX
    └── gen/
      └── database/ent/
      └── api/
      └── errcodes/  ← 生成目录
        └── user_errors_kerrors.pb.go ← 自动生成

---