# Kratos 分层校验架构演示项目

这是一个基于 [Kratos](https://go-kratos.dev/) 框架的分层校验架构演示项目，展示了如何在微服务架构中实现清晰的职责分离和高效的参数校验。

## 🎯 项目特色

- **分层职责明确**: Proto、Service、Biz、Domain、Data 各层职责清晰分离
- **校验策略优化**: 避免重复校验，提升性能和可维护性
- **完整业务示例**: 包含用户管理的完整业务流程演示
- **最佳实践展示**: DDD、Clean Architecture 等设计模式的实际应用
- **测试覆盖完整**: 各层都有对应的单元测试

## 🏗️ 架构概览

```
┌─────────────────┐
│   Proto Layer   │  ← buf.validate 格式校验
├─────────────────┤
│  Service Layer  │  ← DTO转换、上下文处理
├─────────────────┤
│    Biz Layer    │  ← 业务流程校验、权限检查
├─────────────────┤
│  Domain Layer   │  ← 业务规则校验、领域逻辑
├─────────────────┤
│   Data Layer    │  ← 数据持久化、存在性检查
└─────────────────┘
```

## 📋 分层职责

| 层级 | 校验类型 | 示例 | 性能影响 |
|------|----------|------|----------|
| **Proto层** | 格式、类型、基础范围 | 字符串长度、数值范围、正则表达式 | 最低 |
| **Service层** | 参数转换、上下文提取 | DTO转换、操作者信息提取 | 低 |
| **Biz层** | 业务流程、权限、存在性 | 邮箱唯一性、租户权限 | 中等 |
| **Domain层** | 业务规则、状态转换 | 跨字段校验、角色权限、状态机 | 低 |

## 🚀 快速开始

### 环境要求

- Go 1.19+
- Protocol Buffers 3.0+
- Make

### 安装依赖

```bash
# 安装 Kratos CLI
go install github.com/go-kratos/kratos/cmd/kratos/v2@latest

# 安装项目依赖
make init
```

### 生成代码

```bash
# 生成所有代码
make all
```

### 构建和运行

```bash
# 构建项目
make build

# 运行项目
./bin/server -conf configs/config.yaml
```

## 📖 详细文档

- [架构设计详解](docs/architecture_demo.md) - 详细的架构设计说明和示例
- [校验框架文档](docs/validation_framework.md) - 校验框架的使用指南
- [错误处理文档](docs/errors_handling.md) - 错误处理机制说明

## 🧪 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/domain
go test ./internal/service
```

## Install Kratos
```
go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
```
## Create a service
```
# Create a template project
kratos new server

cd server
# Add a proto template
kratos proto add api/server/server.proto
# Generate the proto code
kratos proto client api/server/server.proto
# Generate the source code of service by proto file
kratos proto server api/server/server.proto -t internal/service

go generate ./...
go build -o ./bin/ ./...
./bin/server -conf ./configs
```
## Generate other auxiliary files by Makefile
```
# Download and update dependencies
make init
# Generate API files (include: pb.go, http, grpc, validate, swagger) by proto file
make api
# Generate all files
make all
```
## Automated Initialization (wire)
```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker
```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```

