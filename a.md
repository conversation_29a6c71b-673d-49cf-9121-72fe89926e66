# 参数校验分层优化方案

## 问题分析

当前项目中存在参数校验冗余的问题：

### 三层校验冗余
- **Service层**：手动校验请求参数（如name非空、age范围、id有效性等）
- **Biz层**：调用domain对象的Validate()方法进行业务规则校验
- **Proto层**：使用buf.validate进行基础类型校验（如age <= 100）

### 具体冗余示例
```go
// Service层 (internal/service/user.go)
if req.Age <= 0 || req.Age > 150 {
    return nil, ec.ErrorAgeOutOfRange.WithMetadata(...)
}

// Domain层 (internal/domain/user.go) 
if u.Age < 0 {
    return ec.ErrorAgeOutOfRange.WithMetadata(...)
}

// Proto层 (api/user/v1/user.proto)
int32 age = 2 [(buf.validate.field).int32.lte = 100];
```

## 优化建议：分层职责明确化

### 1. Proto层：基础格式校验

**职责**：数据类型、格式、基础范围校验
**优势**：在序列化/反序列化阶段就能拦截无效数据，性能最优

#### 示例代码：

```protobuf
// api/user/v1/user.proto
message CreateUserRequest {
  // 必填字段 + 长度限制
  string name = 1 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\p{L}\\p{N}\\s]+$"  // 只允许字母、数字、空格
  }];
  
  // 数值范围校验
  int32 age = 2 [(buf.validate.field).int32 = {
    gte: 0,
    lte: 150
  }];
  
  // 必须大于0
  uint64 tenant_id = 3 [(buf.validate.field).uint64.gt = 0];
  
  // 邮箱格式校验（可选字段）
  optional string email = 4 [(buf.validate.field).string = {
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];
}

message ListUsersRequest {
  // 分页参数校验
  uint32 page_size = 1 [(buf.validate.field).uint32 = {
    gte: 1,
    lte: 100
  }];
  uint32 page_num = 2 [(buf.validate.field).uint32.gt = 0];
}
```

### 2. Service层：请求参数转换和基础校验

**职责**：DTO转换、去除重复的业务规则校验

#### 重构前（冗余校验）：
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    // ❌ 重复校验：Proto层已经校验过了
    if req.Name == "" {
        return nil, ec.ErrorInvalidUserParams.WithMetadata(...)
    }
    if req.Age <= 0 || req.Age > 150 {
        return nil, ec.ErrorAgeOutOfRange.WithMetadata(...)
    }
    if req.TenantId <= 0 {
        return nil, ec.ErrorInvalidTenantId.WithMetadata(...)
    }

    user, err := s.uc.Create(ctx, &domain.User{
        Name:     req.Name,
        Age:      int(req.Age),
        TenantID: req.TenantId,
    })
    // ...
}
```

#### 重构后（专注转换）：
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    // ✅ 专注于DTO转换和上下文处理
    user := &domain.User{
        Name:     req.Name,
        Age:      int(req.Age),
        TenantID: req.TenantId,
        Email:    req.Email, // 如果有的话
        // 从上下文中提取操作者信息
        CreatedBy: s.extractOperatorID(ctx),
    }

    createdUser, err := s.uc.Create(ctx, user)
    if err != nil {
        return nil, err
    }
    
    return &userv1.CreateUserReply{
        Id: createdUser.ID,
    }, nil
}

// 辅助方法：提取上下文信息
func (s *UserService) extractOperatorID(ctx context.Context) uint64 {
    if operatorID, ok := ctx.Value("operator_id").(uint64); ok {
        return operatorID
    }
    return 0
}
```

### 3. Domain层：核心业务规则校验

**职责**：复杂业务逻辑、跨字段关联、领域特定规则

#### 重构前（简单校验）：
```go
func (u *User) Validate() error {
    if len(u.Name) == 0 {
        return ec.ErrorInvalidUserParams.WithMetadata(...)
    }
    if u.Age < 0 {
        return ec.ErrorAgeOutOfRange.WithMetadata(...)
    }
    return nil
}
```

#### 重构后（业务规则校验）：
```go
func (u *User) Validate() error {
    // ✅ 复杂业务规则校验
    
    // 1. 跨字段关联校验
    if u.Age < 18 && u.Role == RoleAdmin {
        return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
            "rule": "未成年人不能担任管理员",
            "age":  fmt.Sprintf("%d", u.Age),
            "role": u.Role.String(),
        })
    }
    
    // 2. 领域特定规则
    if u.TenantID > 0 && u.UserType == UserTypeSystem {
        return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
            "rule": "系统用户不能归属于租户",
        })
    }
    
    // 3. 状态转换规则
    if u.Status == UserStatusDeleted && u.LastLoginTime.After(time.Now().AddDate(0, 0, -30)) {
        return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
            "rule": "近30天内活跃的用户不能被删除",
        })
    }
    
    return nil
}

// 业务方法中的复杂校验
func (u *User) CanModify(operatorID uint64) bool {
    // 权限校验逻辑
    if u.ID == operatorID {
        return true // 可以修改自己
    }
    
    // 管理员可以修改同租户用户
    if operator := GetUserByID(operatorID); operator != nil {
        return operator.Role == RoleAdmin && operator.TenantID == u.TenantID
    }
    
    return false
}
```

### 4. Biz层：用例编排和业务流程校验

#### 重构后的完整示例：

```go
func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
    // 1. 领域对象校验（复杂业务规则）
    if err := u.Validate(); err != nil {
        uc.log.Warnw("用户业务规则校验失败", "error", err, "user", u.Name)
        return nil, err
    }
    
    // 2. 业务流程校验（需要查询数据库的校验）
    if u.Email != "" {
        exists, err := uc.repo.ExistsByEmail(ctx, u.Email)
        if err != nil {
            return nil, err
        }
        if exists {
            return nil, ec.ErrorUserEmailExists.WithMetadata(map[string]string{
                "email": u.Email,
            })
        }
    }
    
    // 3. 租户权限校验
    if u.TenantID > 0 {
        if !uc.tenantService.HasCreatePermission(ctx, u.TenantID) {
            return nil, ec.ErrorPermissionDenied.WithMetadata(map[string]string{
                "tenant_id": fmt.Sprintf("%d", u.TenantID),
                "action":    "create_user",
            })
        }
    }
    
    // 4. 执行创建
    createdUser, err := uc.repo.Create(ctx, u)
    if err != nil {
        uc.log.Errorw("创建用户失败", "error", err, "user", u.Name)
        return nil, err
    }
    
    // 5. 业务事件发布
    uc.eventBus.Publish(ctx, &events.UserCreated{
        UserID:   createdUser.ID,
        TenantID: createdUser.TenantID,
        Name:     createdUser.Name,
    })
    
    uc.log.Infow("用户创建成功", "user_id", createdUser.ID, "name", createdUser.Name)
    return createdUser, nil
}
```

## 分层职责总结

| 层级 | 校验类型 | 示例 | 性能影响 |
|------|----------|------|----------|
| **Proto层** | 格式、类型、基础范围 | 字符串长度、数值范围、正则表达式 | 最低 |
| **Service层** | 参数转换、上下文提取 | DTO转换、操作者信息提取 | 低 |
| **Biz层** | 业务流程、权限、存在性 | 邮箱唯一性、租户权限 | 中等 |
| **Domain层** | 业务规则、状态转换 | 跨字段校验、角色权限、状态机 | 低 |

## 优化效果

### 重构前问题：
- 三层重复校验相同规则
- 代码冗余，维护困难
- 性能浪费

### 重构后优势：
- 职责清晰，各层专注自己的校验
- 代码复用性高，易于维护
- 性能优化，早期拦截无效请求
- 错误信息更精确，便于调试

## 实施建议

1. **优先重构Proto层**：完善buf.validate规则，建立基础校验防线
2. **简化Service层**：移除重复校验，专注DTO转换
3. **增强Domain层**：实现复杂业务规则校验
4. **优化Biz层**：专注业务流程编排和跨服务校验
5. **添加中间件**：在HTTP/gRPC层统一处理proto校验错误