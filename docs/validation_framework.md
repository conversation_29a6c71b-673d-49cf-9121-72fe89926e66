# 去冗余校验框架设计方案

## 概述

本方案基于 buf-validate 和 Kratos 中间件，建立了一个分层的校验体系，避免在多个层级重复编写校验逻辑。

## 架构设计

```
Request → gRPC Server → ValidateInterceptor → Service → Biz → Domain
            ↓              ↓                    ↓       ↓        ↓
      Proto Validate   错误转换中间件      跨RPC校验   业务逻辑   业务规则
    (buf-validate)    (统一错误码)      (认证/权限)  (用例逻辑) (域规则)
```

## 各层职责

### 1. Proto 层 (.proto 文件)
**职责**: 基础的数据格式校验
**使用**: buf-validate 标签
**示例**:
```proto
message CreateUserRequest {
  string name = 1 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z]+$"
  }];
  int32 age = 2 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }];
  uint64 tenant_id = 3 [(buf.validate.field).uint64.gt = 0];
}
```

### 2. 中间件层 (Middleware)
**职责**: 
- 自动执行 proto 校验
- 将校验错误转换为业务错误码
- 统一错误格式和响应

**关键组件**:
- `validate.ProtoValidate()`: Kratos 内置的 proto 校验中间件
- `middleware.ValidateInterceptor()`: 自定义错误转换中间件

### 3. Service 层
**职责**: 
- ~~基础参数校验~~ (已移除)
- 跨 RPC 的校验逻辑 (如认证信息、权限检查)
- 业务流程控制

**示例**:
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    // proto 校验已在中间件层完成
    // 这里可以添加跨 RPC 的校验，如认证信息等
    
    user, err := s.uc.Create(ctx, &domain.User{
        Name:     req.Name,
        Age:      int(req.Age),
        TenantID: req.TenantId,
    })
    if err != nil {
        return nil, err
    }
    return &userv1.CreateUserReply{Id: user.ID}, nil
}
```

### 4. Biz 层
**职责**:
- ~~基础参数校验~~ (已移除)
- 业务用例逻辑
- 调用 Domain.Validate 进行业务规则校验

**示例**:
```go
func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
    // 验证用户域对象的业务规则
    if err := u.Validate(); err != nil {
        return nil, err
    }
    
    // 执行业务逻辑
    return uc.repo.Create(ctx, u)
}
```

### 5. Domain 层
**职责**: 
- 业务规则校验 (非格式校验)
- 复杂的业务逻辑验证
- 跨字段的一致性检查

**示例**:
```go
func (u *User) Validate() error {
    // 复杂的业务规则校验
    // 例如：特殊年龄段的业务规则校验
    if u.Age > 65 {
        // 老年用户的特殊业务规则校验
        // 这里可以添加更复杂的业务逻辑
    }
    
    return nil
}
```

## 错误处理流程

1. **Proto 校验失败**: 
   - buf-validate 产生 `InvalidArgument` 错误
   - `ValidateInterceptor` 捕获并转换为业务错误码
   - 返回统一的错误响应

2. **业务规则校验失败**:
   - Domain.Validate() 返回业务错误码
   - 直接返回给客户端

3. **错误码统一**:
   - 使用 `ec` 包封装所有错误
   - 支持错误元数据 (field, rule, value)
   - 自动转换为 gRPC Status

## 配置步骤

### 1. 更新 proto 文件
添加 buf-validate 导入和校验规则:
```proto
import "buf/validate/validate.proto";

message YourRequest {
  string field = 1 [(buf.validate.field).string.min_len = 1];
}
```

### 2. 注册中间件
在 gRPC 服务器配置中添加:
```go
grpc.Middleware(
    validate.ProtoValidate(),
    middleware.ValidateInterceptor(),
    // ... 其他中间件
)
```

### 3. 重构现有代码
- 移除 Service 层的重复校验
- 移除 Biz 层的基础参数校验
- 保留 Domain 层的业务规则校验

## 优势

1. **避免重复**: 基础校验只在 proto 层定义一次
2. **统一错误**: 所有校验错误都转换为统一的业务错误码
3. **清晰分层**: 各层职责明确，减少耦合
4. **易于维护**: 校验规则集中管理，修改方便
5. **类型安全**: 基于 proto 定义，编译时检查

## 注意事项

1. 需要确保 buf-validate 版本兼容性
2. 复杂的跨字段校验仍需要在 Domain 层处理
3. 认证、权限等跨 RPC 校验保留在 Service 层
4. 错误转换逻辑需要根据实际的 buf-validate 错误格式调整
