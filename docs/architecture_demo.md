# 分层校验架构演示

本项目是一个基于 Kratos 框架的分层校验架构演示项目，展示了如何在微服务架构中实现清晰的职责分离和高效的参数校验。

## 架构概览

本项目采用了经典的分层架构，每一层都有明确的职责：

```
┌─────────────────┐
│   Proto Layer   │  ← buf.validate 格式校验
├─────────────────┤
│  Service Layer  │  ← DTO转换、上下文处理
├─────────────────┤
│    Biz Layer    │  ← 业务流程校验、权限检查
├─────────────────┤
│  Domain Layer   │  ← 业务规则校验、领域逻辑
├─────────────────┤
│   Data Layer    │  ← 数据持久化、存在性检查
└─────────────────┘
```

## 分层职责详解

### 1. Proto层 - 格式校验
**职责**: 数据类型、格式、基础范围校验
**技术**: buf.validate
**优势**: 在序列化/反序列化阶段就能拦截无效数据，性能最优

**示例**:
```protobuf
message CreateUserRequest {
  // 用户名：支持中文、英文字母
  string name = 1 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z\\s]+$"
  }];
  
  // 年龄：1-150岁
  int32 age = 2 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }];
  
  // 邮箱：可选字段，如果提供则必须符合邮箱格式
  optional string email = 4 [(buf.validate.field).string = {
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];
}
```

### 2. Service层 - DTO转换
**职责**: DTO转换、上下文信息提取
**特点**: 不进行重复的业务规则校验

**示例**:
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    // ✅ 专注于DTO转换和上下文处理，proto校验已在中间件层完成
    domainUser := &domain.User{
        Name:     req.Name,
        Age:      int(req.Age),
        TenantID: req.TenantId,
        Email:    getOptionalString(req.Email),
        Phone:    getOptionalString(req.Phone),
        Role:     convertProtoRole(req.Role),
        Status:   domain.UserStatusActive,
        // 从上下文中提取操作者信息
        CreatedBy: s.extractOperatorID(ctx),
    }
    
    // 调用业务层创建用户（业务校验在Biz层进行）
    return s.uc.Create(ctx, domainUser)
}
```

### 3. Domain层 - 业务规则校验
**职责**: 复杂业务逻辑、跨字段关联、领域特定规则
**特点**: 不依赖外部服务，纯领域逻辑

**示例**:
```go
func (u *User) Validate() error {
    // 1. 跨字段关联校验：未成年人不能担任管理员
    if u.Age < 18 && u.Role == UserRoleAdmin {
        return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
            "rule":   "未成年人不能担任管理员",
            "age":    fmt.Sprintf("%d", u.Age),
            "role":   u.Role.String(),
            "reason": "管理员角色需要年满18岁",
        })
    }
    
    // 2. 敏感词汇检查
    if strings.Contains(strings.ToLower(u.Name), "admin") && 
       u.Role != UserRoleAdmin && u.Role != UserRoleSuperAdmin {
        return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
            "rule":   "普通用户名不能包含管理员关键词",
            "name":   u.Name,
            "role":   u.Role.String(),
            "reason": "用户名包含敏感词汇",
        })
    }
    
    return nil
}
```

### 4. Biz层 - 业务流程校验
**职责**: 业务流程编排、权限校验、存在性检查
**特点**: 可以调用外部服务和数据库

**示例**:
```go
func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
    // 1. 领域对象校验（复杂业务规则）
    if err := u.Validate(); err != nil {
        uc.log.Warnw("用户业务规则校验失败", "error", err, "user", u.Name)
        return nil, err
    }
    
    // 2. 业务流程校验：邮箱唯一性检查
    if u.Email != "" {
        exists, err := uc.repo.ExistsByEmail(ctx, u.Email)
        if err != nil {
            return nil, err
        }
        if exists {
            return nil, ec.ErrorUserEmailExists.WithMetadata(map[string]string{
                "email": u.Email,
            })
        }
    }
    
    // 3. 租户权限校验
    if u.TenantID > 0 && uc.tenantService != nil {
        if !uc.tenantService.HasCreatePermission(ctx, u.TenantID) {
            return nil, ec.ErrorPermissionDenied.WithMetadata(map[string]string{
                "tenant_id": fmt.Sprintf("%d", u.TenantID),
                "action":    "create_user",
            })
        }
    }
    
    // 4. 执行创建
    createdUser, err := uc.repo.Create(ctx, u)
    if err != nil {
        return nil, err
    }
    
    // 5. 业务事件发布
    if uc.eventBus != nil {
        uc.eventBus.Publish(ctx, &UserCreated{
            UserID:   createdUser.ID,
            TenantID: createdUser.TenantID,
            Name:     createdUser.Name,
        })
    }
    
    return createdUser, nil
}
```

## 错误处理策略

项目采用了统一的错误处理机制：

1. **Proto层错误**: 通过中间件自动转换为业务错误码
2. **业务错误**: 使用结构化的错误信息，包含详细的元数据
3. **系统错误**: 记录详细日志，返回通用错误信息

## 使用示例

### 1. 启动项目
```bash
# 生成代码
make all

# 构建项目
make build

# 运行项目
./bin/server -conf configs/config.yaml
```

### 2. API调用示例

**创建用户**:
```bash
curl -X POST http://localhost:8000/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "age": 25,
    "tenant_id": 1001,
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "USER_ROLE_USER"
  }'
```

**获取用户列表**:
```bash
curl "http://localhost:8000/v1/users?page_size=10&page_num=1"
```

### 3. 校验演示

**格式校验失败**:
```bash
# 年龄超出范围
curl -X POST http://localhost:8000/v1/users \
  -H "Content-Type: application/json" \
  -d '{"name": "测试", "age": 200, "tenant_id": 1001}'
# 返回: {"code": 400, "message": "年龄超出有效范围"}
```

**业务规则校验失败**:
```bash
# 未成年管理员
curl -X POST http://localhost:8000/v1/users \
  -H "Content-Type: application/json" \
  -d '{"name": "小明", "age": 16, "tenant_id": 1001, "role": "USER_ROLE_ADMIN"}'
# 返回: {"code": 422, "message": "业务规则违反", "metadata": {"rule": "未成年人不能担任管理员"}}
```

## 扩展指南

### 添加新的校验规则

1. **Proto层**: 在 `.proto` 文件中添加 `buf.validate` 规则
2. **Domain层**: 在 `Validate()` 方法中添加业务规则
3. **Biz层**: 添加需要外部依赖的校验逻辑

### 添加新的业务字段

1. 更新 `user.proto` 中的消息定义
2. 更新 `internal/data/schema/user.go` 中的数据库字段
3. 重新生成代码: `make all`

## 最佳实践

1. **职责分离**: 每层只处理自己职责范围内的校验
2. **错误信息**: 提供清晰、结构化的错误信息
3. **性能优化**: 在最早的层次拦截无效数据
4. **可测试性**: 每层都可以独立测试
5. **可维护性**: 避免重复代码，集中管理校验规则
