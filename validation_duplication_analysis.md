# 重复校验分析报告 - Service 层与 Biz 层对比表

## 概述
通过对 `internal/service` 和 `internal/biz` 层代码的详细审查，发现了多处重复的校验逻辑。本报告详细记录了每个字段出现重复校验的具体位置与行号。

## 重复校验详情

### 1. 用户名称 (Name) 非空校验

| 层级 | 文件路径 | 方法 | 行号 | 校验逻辑 |
|------|----------|------|------|----------|
| Service | `/internal/service/user.go` | `CreateUser` | 31-36 | `if req.Name == ""` |
| Service | `/internal/service/user.go` | `UpdateUser` | 74-79 | `if req.Name == ""` |
| Domain | `/internal/domain/user.go` | `Validate` | 37-42 | `if len(u.Name) == 0` |

**重复说明**: Name 字段的非空校验在 Service 层的 CreateUser/UpdateUser 方法和 Domain 层的 Validate 方法中重复实现。

### 2. 年龄 (Age) 范围校验

| 层级 | 文件路径 | 方法 | 行号 | 校验逻辑 |
|------|----------|------|------|----------|
| Service | `/internal/service/user.go` | `CreateUser` | 37-43 | `if req.Age <= 0 \|\| req.Age > 150` |
| Service | `/internal/service/user.go` | `UpdateUser` | 80-86 | `if req.Age <= 0 \|\| req.Age > 150` |
| Domain | `/internal/domain/user.go` | `Validate` | 44-50 | `if u.Age < 0` (部分校验) |

**重复说明**: 
- Service 层在两个方法中重复实现了完全相同的年龄范围校验 (1-150)
- Domain 层实现了部分年龄校验 (>= 0)，校验规则不一致

### 3. 用户ID (Id) 正数校验

| 层级 | 文件路径 | 方法 | 行号 | 校验逻辑 |
|------|----------|------|------|----------|
| Service | `/internal/service/user.go` | `UpdateUser` | 67-73 | `if req.Id <= 0` |
| Service | `/internal/service/user.go` | `DeleteUser` | 104-110 | `if req.Id <= 0` |

**重复说明**: ID 字段的正数校验在 Service 层的两个不同方法中重复实现。

### 4. 租户ID (TenantId) 正数校验

| 层级 | 文件路径 | 方法 | 行号 | 校验逻辑 |
|------|----------|------|------|----------|
| Service | `/internal/service/user.go` | `CreateUser` | 44-50 | `if req.TenantId <= 0` |

**重复说明**: 目前只在 CreateUser 方法中校验，但这种校验逻辑可能在其他地方也需要。

### 5. 分页参数校验

| 层级 | 文件路径 | 方法 | 行号 | 校验逻辑 |
|------|----------|------|------|----------|
| Service | `/internal/service/user.go` | `ListUsers` | 121-127 | `if req.PageNum <= 0` |
| Service | `/internal/service/user.go` | `ListUsers` | 128-134 | `if req.PageSize <= 0 \|\| req.PageSize > 100` |
| Biz | `/internal/biz/user.go` | `List` | 55-61 | `if pageNum <= 0` |
| Biz | `/internal/biz/user.go` | `List` | 62-68 | `if pageSize <= 0 \|\| pageSize > 100` |

**重复说明**: 分页参数 (PageNum 和 PageSize) 的校验逻辑在 Service 层和 Biz 层完全重复实现。

## 校验错误处理重复

### 错误消息格式重复
所有校验都使用相同的错误处理模式：
```go
return nil, ec.ErrorXXX.WithMetadata(map[string]string{
    "field": "字段名",
    "value": "字段值", 
    "rule":  "校验规则",
})
```

这种模式在以下位置重复出现：
- Service 层: 31-36, 37-43, 44-50, 67-73, 74-79, 80-86, 104-110, 121-127, 128-134 行
- Domain 层: 37-42, 44-50 行  
- Biz 层: 55-61, 62-68 行

## 重构建议

### 1. 消除 Service 与 Domain 层重复
- **问题**: Service 层和 Domain 层都在做基础字段校验
- **建议**: 将基础字段校验统一到 Domain 层的 Validate 方法中

### 2. 消除 Service 与 Biz 层重复  
- **问题**: 分页参数校验在两层都实现
- **建议**: 将分页参数校验移至 Biz 层，Service 层直接传递参数

### 3. 消除同层内重复
- **问题**: Service 层内多个方法重复相同的校验逻辑
- **建议**: 提取通用校验函数，避免代码重复

### 4. 统一校验规则
- **问题**: 年龄校验在不同层级规则不一致
- **建议**: 统一制定校验规则标准，确保一致性

## 影响评估

### 维护成本
- 当前重复校验导致修改校验规则时需要同时修改多个文件
- 容易出现校验规则不一致的问题
- 增加了代码维护的复杂度

### 代码质量  
- 违反了 DRY (Don't Repeat Yourself) 原则
- 降低了代码的可读性和可维护性
- 增加了出错的可能性

### 性能影响
- 重复校验会导致不必要的性能开销
- 特别是在高并发场景下，重复校验会影响响应速度

## 总结

共发现 **5 类重复校验**，涉及 **13 处具体重复位置**。建议按照上述重构建议进行代码优化，以提高代码质量和可维护性。
