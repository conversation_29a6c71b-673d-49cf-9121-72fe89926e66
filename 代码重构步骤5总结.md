# 步骤5: 代码重构示例总结

## 概述
本次重构将参数校验的职责进行了合理分层，消除了重复校验，建立了清晰的校验架构。

## 重构内容

### 1. .proto 修改 + 重新生成

**修改内容：**
- 为 `CreateUserRequest.tenant_id` 增加了上限校验 `lte: 999999`
- 所有字段都添加了完整的 buf-validate 规则

**具体修改：**
```protobuf
// 修改前
uint64 tenant_id = 3 [(buf.validate.field).uint64.gt = 0];

// 修改后  
uint64 tenant_id = 3 [(buf.validate.field).uint64 = {
  gt: 0,
  lte: 999999
}];
```

### 2. 新增 middleware/validator.go: Validate() 拦截器

**新增功能：**
- 添加了 `Validate()` 方法作为通用校验器入口
- 保持原有的错误转换功能

**代码：**
```go
// Validate 通用校验器，执行请求参数的校验
func Validate() middleware.Middleware {
	return ValidateInterceptor()
}
```

### 3. 删除 UserService.CreateUser 中冗余校验

**重构内容：**
- 删除了 Service 层中的 Name/Age/TenantId 格式校验（这些已在 proto 层处理）
- 保留了领域层业务校验的调用
- 简化了代码逻辑

**主要变化：**
```go
// 重构前：包含大量参数校验
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
	// 参数验证
	if req.Name == "" {
		return nil, ec.ErrorInvalidUserParams.WithMetadata(...)
	}
	if req.Age <= 0 || req.Age > 150 {
		return nil, ec.ErrorAgeOutOfRange.WithMetadata(...)
	}
	if req.TenantId <= 0 {
		return nil, ec.ErrorInvalidTenantId.WithMetadata(...)
	}
	// ... 业务逻辑
}

// 重构后：简化校验，专注业务逻辑
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
	// 创建领域对象
	domainUser := &domain.User{
		Name:     req.Name,
		Age:      int(req.Age),
		TenantID: req.TenantId,
	}
	
	// 执行领域层业务校验
	if err := domainUser.Validate(); err != nil {
		return nil, err
	}
	
	user, err := s.uc.Create(ctx, domainUser)
	// ... 处理结果
}
```

### 4. 保留租户 ID >0 的领域校验在 domain.User.Validate

**重构内容：**
- 将租户 ID > 0 的校验从 Service 层移到 Domain 层
- 增加了业务规则校验的框架和示例
- 明确了领域校验的职责边界

**代码变化：**
```go
// Validate 域对象的业务规则校验（不同于 proto 格式校验）
func (u *User) Validate() error {
	// 基本的格式校验已在 proto 层完成，这里主要做业务规则校验
	
	// 租户 ID 的领域校验：检查租户是否有效
	if u.TenantID <= 0 {
		return ec.ErrorInvalidTenantId.WithMetadata(map[string]string{
			"field": "tenant_id",
			"reason": "租户ID必须大于0",
		})
	}
	
	// 业务规则校验示例：
	// 1. VIP 用户名字不能包含敏感词汇
	// 2. 特定年龄段的用户需要额外的验证
	// 3. 特殊租户的用户名规则
	
	// 特殊年龄段的业务规则校验
	if u.Age > 65 {
		// 老年用户的特殊业务规则校验
		// 例如：需要监护人同意等
		// 这里可以添加更复杂的业务逻辑
	}
	
	// 租户特定的业务规则
	if u.TenantID == 999999 {
		// 测试租户的特殊限制
		// 例如：测试租户不能创建真实用户
	}
	
	return nil
}
```

## 校验架构层次

重构后的校验架构分为三层：

1. **Proto 层（buf-validate）**：格式校验
   - 字段长度、数值范围、正则表达式等
   - 自动执行，无需手动编码

2. **中间件层（middleware/validate.go）**：错误转换
   - 将 proto 校验错误转换为业务错误码
   - 统一错误处理格式

3. **领域层（domain.User.Validate）**：业务规则校验
   - 复杂的业务逻辑校验
   - 跨字段关联校验
   - 特定业务场景的规则

## 文件结构示例

```
kratos-layout/
├── api/user/v1/
│   └── user.proto                    # [修改] 增强 buf-validate 规则
├── internal/
│   ├── middleware/
│   │   └── validate.go              # [修改] 新增 Validate() 方法
│   ├── service/ 
│   │   └── user.go                  # [修改] 删除冗余校验，简化逻辑
│   └── domain/
│       └── user.go                  # [修改] 增强领域校验逻辑
└── 代码重构步骤5总结.md              # [新增] 本总结文档
```

## 重构效果

1. **消除重复**：删除了 Service 层中与 proto 层重复的格式校验
2. **职责清晰**：每层校验都有明确的职责范围
3. **易于维护**：校验规则集中管理，便于修改和扩展
4. **性能提升**：减少了重复的参数校验开销
5. **代码简洁**：Service 层代码更加简洁，专注业务逻辑

## 校验流程

```
请求 → Proto层校验 → 中间件错误转换 → Service层 → Domain层业务校验 → 业务逻辑处理
       (格式校验)     (错误统一化)      (调用)    (业务规则校验)      (核心逻辑)
```

这样的架构设计保证了校验的完整性和高效性，同时避免了代码重复和职责混乱的问题。
