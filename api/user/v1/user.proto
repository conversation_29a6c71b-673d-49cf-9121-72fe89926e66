syntax = "proto3";

package user.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "buf/validate/validate.proto";

option go_package = "kratos-layout/api/user/v1;v1";

// 用户服务
service User {
  // 创建用户
  rpc CreateUser (CreateUserRequest) returns (CreateUserReply) {
    option (google.api.http) = {
      post: "/v1/users"
      body: "*"
    };
  }
  // 更新用户
  rpc UpdateUser (UpdateUserRequest) returns (UpdateUserReply) {
    option (google.api.http) = {
      put: "/v1/users/{id}"
      body: "*"
    };
  }
  // 删除用户
  rpc DeleteUser (DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/users/{id}"
    };
  }
  // 获取用户列表
  rpc ListUsers (ListUsersRequest) returns (ListUsersReply) {
    option (google.api.http) = {
      get: "/v1/users"
    };
  }
}

// 创建用户请求
message CreateUserRequest {
  string name = 1 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z]+$"
  }];
  int32 age = 2 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }];
  uint64 tenant_id = 3 [(buf.validate.field).uint64 = {
    gt: 0,
    lte: 999999
  }];
}

// 创建用户响应
message CreateUserReply {
  uint64 id = 1;
}

// 更新用户请求
message UpdateUserRequest {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  string name = 2 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z]+$"
  }];
  int32 age = 3 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }];
}

// 更新用户响应
message UpdateUserReply {
  bool success = 1;
}

// 删除用户请求
message DeleteUserRequest {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户列表请求
message ListUsersRequest {
  uint32 page_size = 1 [(buf.validate.field).uint32 = {
    gte: 1,
    lte: 100
  }];
  uint32 page_num = 2 [(buf.validate.field).uint32.gt = 0];
}

// 用户信息
message UserInfo {
  uint64 id = 1;
  string name = 2;
  int32 age = 3;
  uint64 tenant_id = 4;
}

// 用户列表响应
message ListUsersReply {
  repeated UserInfo users = 1;
  uint32 total = 2;
} 
