syntax = "proto3";

package user.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "buf/validate/validate.proto";

option go_package = "kratos-layout/api/user/v1;v1";

// 用户服务
service User {
  // 创建用户
  rpc CreateUser (CreateUserRequest) returns (CreateUserReply) {
    option (google.api.http) = {
      post: "/v1/users"
      body: "*"
    };
  }
  // 更新用户
  rpc UpdateUser (UpdateUserRequest) returns (UpdateUserReply) {
    option (google.api.http) = {
      put: "/v1/users/{id}"
      body: "*"
    };
  }
  // 删除用户
  rpc DeleteUser (DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/users/{id}"
    };
  }
  // 获取用户列表
  rpc ListUsers (ListUsersRequest) returns (ListUsersReply) {
    option (google.api.http) = {
      get: "/v1/users"
    };
  }
}

// 用户角色枚举
enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_USER = 1;        // 普通用户
  USER_ROLE_ADMIN = 2;       // 管理员
  USER_ROLE_SUPER_ADMIN = 3; // 超级管理员
}

// 用户状态枚举
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;    // 活跃
  USER_STATUS_INACTIVE = 2;  // 非活跃
  USER_STATUS_SUSPENDED = 3; // 暂停
  USER_STATUS_DELETED = 4;   // 已删除
}

// 创建用户请求
message CreateUserRequest {
  // 用户名：支持中文、英文字母
  string name = 1 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z\\s]+$"
  }];

  // 年龄：1-150岁
  int32 age = 2 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }];

  // 租户ID：必须大于0
  uint64 tenant_id = 3 [(buf.validate.field).uint64 = {
    gt: 0,
    lte: 999999
  }];

  // 邮箱：可选字段，如果提供则必须符合邮箱格式
  optional string email = 4 [(buf.validate.field).string = {
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 手机号：可选字段，支持中国大陆手机号格式
  optional string phone = 5 [(buf.validate.field).string = {
    pattern: "^1[3-9]\\d{9}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 用户角色：默认为普通用户
  UserRole role = 6 [(buf.validate.field).enum = {
    defined_only: true,
    not_in: [0] // 不允许未指定状态
  }];
}

// 创建用户响应
message CreateUserReply {
  uint64 id = 1;
}

// 更新用户请求
message UpdateUserRequest {
  // 用户ID：必须大于0
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];

  // 用户名：支持中文、英文字母
  optional string name = 2 [(buf.validate.field).string = {
    min_len: 1,
    max_len: 50,
    pattern: "^[\\u4e00-\\u9fa5a-zA-Z\\s]+$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 年龄：1-150岁
  optional int32 age = 3 [(buf.validate.field).int32 = {
    gte: 1,
    lte: 150
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 邮箱：可选字段
  optional string email = 4 [(buf.validate.field).string = {
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 手机号：可选字段
  optional string phone = 5 [(buf.validate.field).string = {
    pattern: "^1[3-9]\\d{9}$"
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];

  // 用户状态：可选字段
  optional UserStatus status = 6 [(buf.validate.field).enum = {
    defined_only: true,
    not_in: [0] // 不允许未指定状态
  }, (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED];
}

// 更新用户响应
message UpdateUserReply {
  bool success = 1;
}

// 删除用户请求
message DeleteUserRequest {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户列表请求
message ListUsersRequest {
  uint32 page_size = 1 [(buf.validate.field).uint32 = {
    gte: 1,
    lte: 100
  }];
  uint32 page_num = 2 [(buf.validate.field).uint32.gt = 0];
}

// 用户信息
message UserInfo {
  uint64 id = 1;
  string name = 2;
  int32 age = 3;
  uint64 tenant_id = 4;
  string email = 5;
  string phone = 6;
  UserRole role = 7;
  UserStatus status = 8;
  int64 created_at = 9;    // 创建时间（Unix时间戳）
  int64 updated_at = 10;   // 更新时间（Unix时间戳）
}

// 用户列表响应
message ListUsersReply {
  repeated UserInfo users = 1;
  uint32 total = 2;
} 
