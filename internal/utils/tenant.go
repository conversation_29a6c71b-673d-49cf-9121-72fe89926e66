package utils

import (
	"context"
	"fmt"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos-layout/internal/gen/database/ent/intercept"
)

// 定义租户ID的context key
type tenantKey struct{}

// 设置租户ID到context
func WithTenant(ctx context.Context, tenantID uint64) context.Context {
	return context.WithValue(ctx, tenantKey{}, tenantID)
}

// 从context获取租户ID
func TenantFromContext(ctx context.Context) (uint64, error) {
	tid, ok := ctx.Value(tenantKey{}).(uint64)
	if !ok {
		tid = GetTenantIDFromMeta(ctx)
		if tid == 0 {
			// 如果没有租户ID,返回错误
			return 0, fmt.Errorf("tenant id not found in context")
		}
	}
	return tid, nil
}

// 多租户拦截器
func TenantInterceptor() ent.Interceptor {
	return intercept.TraverseFunc(func(ctx context.Context, q intercept.Query) error {
		// Apply a predicate/filter to all queries.
		tenantID, err := TenantFromContext(ctx)
		if err != nil {
			return err
		}
		q.WhereP(sql.FieldEQ("tenant_id", tenantID))
		return nil
	})
}

// 多租户Hook
func TenantHook() ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			tenantID, err := TenantFromContext(ctx)
			if err != nil {
				return nil, err
			}

			// 创建时设置租户ID
			if m.Op().Is(ent.OpCreate) {
				if setter, ok := m.(interface{ SetTenantID(uint64) }); ok {
					setter.SetTenantID(tenantID)
				}
			}

			// 更新和删除时添加租户过滤
			if m.Op().Is(ent.OpUpdateOne | ent.OpDeleteOne | ent.OpUpdate | ent.OpDelete) {
				if pred, ok := m.(interface{ WhereP(...func(*sql.Selector)) }); ok {
					pred.WhereP(sql.FieldEQ("tenant_id", tenantID))
				}
			}

			return next.Mutate(ctx, m)
		})
	}
}
