package middleware

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestValidateInterceptor(t *testing.T) {
	tests := []struct {
		name        string
		inputError  error
		expectError bool
		expectField string
	}{
		{
			name:        "no error",
			inputError:  nil,
			expectError: false,
		},
		{
			name:        "grpc invalid argument error",
			inputError:  status.Error(codes.InvalidArgument, "age: must be <= 150"),
			expectError: true,
			expectField: "age",
		},
		{
			name:        "kratos validate error",
			inputError:  errors.New(400, "VALIDATE_ERROR", "name: required"),
			expectError: true,
			expectField: "name",
		},
		{
			name:        "other error",
			inputError:  errors.New(500, "INTERNAL_ERROR", "internal error"),
			expectError: true,
		},
	}

	interceptor := ValidateInterceptor()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := func(ctx context.Context, req interface{}) (interface{}, error) {
				return "response", tt.inputError
			}

			wrappedHandler := interceptor(handler)
			_, err := wrappedHandler(context.Background(), "request")

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, but got nil")
				}
			} else {
				if err != nil {
					t.Errorf("expected no error, but got %v", err)
				}
			}
		})
	}
}

func TestParseValidateError(t *testing.T) {
	tests := []struct {
		name          string
		message       string
		expectedField string
		expectedRule  string
	}{
		{
			name:          "simple error",
			message:       "age: must be <= 150",
			expectedField: "age",
			expectedRule:  "must be <= 150",
		},
		{
			name:          "complex error",
			message:       "name: min_len requirement not met",
			expectedField: "name",
			expectedRule:  "min_len requirement not met",
		},
		{
			name:          "no colon",
			message:       "invalid input",
			expectedField: "unknown",
			expectedRule:  "invalid input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			field, rule := parseValidateError(tt.message)
			if field != tt.expectedField {
				t.Errorf("expected field %s, got %s", tt.expectedField, field)
			}
			if rule != tt.expectedRule {
				t.Errorf("expected rule %s, got %s", tt.expectedRule, rule)
			}
		})
	}
}
