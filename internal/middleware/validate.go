package middleware

import (
	"context"
	"strings"

	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ValidateInterceptor 校验拦截器，将 buf-validate 错误转换为统一的错误码
func ValidateInterceptor() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			reply, err := handler(ctx, req)
			if err != nil {
				// 检查是否是 buf-validate 产生的错误
				if st, ok := status.FromError(err); ok && st.Code() == codes.InvalidArgument {
					return nil, convertValidateError(st.Message())
				}
				// 检查是否是 kratos validate 中间件的错误
				if e := errors.FromError(err); e.Reason == "VALIDATE_ERROR" {
					return nil, convertValidateError(e.Message)
				}
			}
			return reply, err
		}
	}
}

// Validate 通用校验器，执行请求参数的校验
func Validate() middleware.Middleware {
	return ValidateInterceptor()
}

// convertValidateError 将校验错误转换为业务错误码
func convertValidateError(message string) error {
	// 解析错误信息，提取字段名和校验规则
	fieldName, rule := parseValidateError(message)
	
	metadata := map[string]string{
		"field": fieldName,
		"rule":  rule,
	}

	// 根据字段名和规则返回对应的错误码
	switch {
	case strings.Contains(fieldName, "age"):
		return ec.ErrorAgeOutOfRange.WithMetadata(metadata)
	case strings.Contains(fieldName, "tenant_id"):
		return ec.ErrorInvalidTenantId.WithMetadata(metadata)
	case strings.Contains(fieldName, "page_"):
		return ec.ErrorInvalidParameter.WithMetadata(metadata)
	default:
		return ec.ErrorInvalidUserParams.WithMetadata(metadata)
	}
}

// parseValidateError 解析校验错误信息
func parseValidateError(message string) (fieldName, rule string) {
	// 简单的错误信息解析
	// 实际项目中可以根据 buf-validate 的具体错误格式进行更精确的解析
	parts := strings.Split(message, ":")
	if len(parts) >= 2 {
		fieldName = strings.TrimSpace(parts[0])
		rule = strings.TrimSpace(parts[1])
	} else {
		fieldName = "unknown"
		rule = message
	}
	return
}
