// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: errcodes/user_errors.proto

package errcodes

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserErrorReason 定义用户服务相关错误码
type UserErrorReason int32

const (
	// 用户未找到
	UserErrorReason_USER_NOT_FOUND UserErrorReason = 0
	// 用户已存在
	UserErrorReason_USER_ALREADY_EXISTS UserErrorReason = 1
	// 无效的用户参数
	UserErrorReason_INVALID_USER_PARAMS UserErrorReason = 2
	// 租户ID无效
	UserErrorReason_INVALID_TENANT_ID UserErrorReason = 3
	// 用户名已被使用
	UserErrorReason_USERNAME_ALREADY_TAKEN UserErrorReason = 4
	// 年龄超出范围
	UserErrorReason_AGE_OUT_OF_RANGE UserErrorReason = 5
	// 业务规则违反
	UserErrorReason_BUSINESS_RULE_VIOLATION UserErrorReason = 6
	// 用户邮箱已存在
	UserErrorReason_USER_EMAIL_EXISTS UserErrorReason = 7
	// 用户手机号已存在
	UserErrorReason_USER_PHONE_EXISTS UserErrorReason = 8
	// 权限被拒绝
	UserErrorReason_PERMISSION_DENIED UserErrorReason = 9
)

// Enum value maps for UserErrorReason.
var (
	UserErrorReason_name = map[int32]string{
		0: "USER_NOT_FOUND",
		1: "USER_ALREADY_EXISTS",
		2: "INVALID_USER_PARAMS",
		3: "INVALID_TENANT_ID",
		4: "USERNAME_ALREADY_TAKEN",
		5: "AGE_OUT_OF_RANGE",
		6: "BUSINESS_RULE_VIOLATION",
		7: "USER_EMAIL_EXISTS",
		8: "USER_PHONE_EXISTS",
		9: "PERMISSION_DENIED",
	}
	UserErrorReason_value = map[string]int32{
		"USER_NOT_FOUND":          0,
		"USER_ALREADY_EXISTS":     1,
		"INVALID_USER_PARAMS":     2,
		"INVALID_TENANT_ID":       3,
		"USERNAME_ALREADY_TAKEN":  4,
		"AGE_OUT_OF_RANGE":        5,
		"BUSINESS_RULE_VIOLATION": 6,
		"USER_EMAIL_EXISTS":       7,
		"USER_PHONE_EXISTS":       8,
		"PERMISSION_DENIED":       9,
	}
)

func (x UserErrorReason) Enum() *UserErrorReason {
	p := new(UserErrorReason)
	*p = x
	return p
}

func (x UserErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_errcodes_user_errors_proto_enumTypes[0].Descriptor()
}

func (UserErrorReason) Type() protoreflect.EnumType {
	return &file_errcodes_user_errors_proto_enumTypes[0]
}

func (x UserErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserErrorReason.Descriptor instead.
func (UserErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_errcodes_user_errors_proto_rawDescGZIP(), []int{0}
}

var File_errcodes_user_errors_proto protoreflect.FileDescriptor

const file_errcodes_user_errors_proto_rawDesc = "" +
	"\n" +
	"\x1aerrcodes/user_errors.proto\x12\berrcodes\x1a\x13errors/errors.proto*\x98\x04\n" +
	"\x0fUserErrorReason\x12*\n" +
	"\x0eUSER_NOT_FOUND\x10\x00\x1a\x16\xa8E\x94\x03\xb2E\x0f用户不存在\x12/\n" +
	"\x13USER_ALREADY_EXISTS\x10\x01\x1a\x16\xa8E\x99\x03\xb2E\x0f用户已存在\x125\n" +
	"\x13INVALID_USER_PARAMS\x10\x02\x1a\x1c\xa8E\x90\x03\xb2E\x15无效的用户参数\x12/\n" +
	"\x11INVALID_TENANT_ID\x10\x03\x1a\x18\xa8E\x90\x03\xb2E\x11无效的租户ID\x128\n" +
	"\x16USERNAME_ALREADY_TAKEN\x10\x04\x1a\x1c\xa8E\x99\x03\xb2E\x15用户名已被使用\x125\n" +
	"\x10AGE_OUT_OF_RANGE\x10\x05\x1a\x1f\xa8E\x90\x03\xb2E\x18年龄超出有效范围\x126\n" +
	"\x17BUSINESS_RULE_VIOLATION\x10\x06\x1a\x19\xa8E\xa6\x03\xb2E\x12业务规则违反\x120\n" +
	"\x11USER_EMAIL_EXISTS\x10\a\x1a\x19\xa8E\x99\x03\xb2E\x12邮箱已被使用\x123\n" +
	"\x11USER_PHONE_EXISTS\x10\b\x1a\x1c\xa8E\x99\x03\xb2E\x15手机号已被使用\x12*\n" +
	"\x11PERMISSION_DENIED\x10\t\x1a\x13\xa8E\x93\x03\xb2E\f权限不足\x1a\x04\xa0E\x90\x03BCZAgithub.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodesb\x06proto3"

var (
	file_errcodes_user_errors_proto_rawDescOnce sync.Once
	file_errcodes_user_errors_proto_rawDescData []byte
)

func file_errcodes_user_errors_proto_rawDescGZIP() []byte {
	file_errcodes_user_errors_proto_rawDescOnce.Do(func() {
		file_errcodes_user_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errcodes_user_errors_proto_rawDesc), len(file_errcodes_user_errors_proto_rawDesc)))
	})
	return file_errcodes_user_errors_proto_rawDescData
}

var file_errcodes_user_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errcodes_user_errors_proto_goTypes = []any{
	(UserErrorReason)(0), // 0: errcodes.UserErrorReason
}
var file_errcodes_user_errors_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_errcodes_user_errors_proto_init() }
func file_errcodes_user_errors_proto_init() {
	if File_errcodes_user_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errcodes_user_errors_proto_rawDesc), len(file_errcodes_user_errors_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errcodes_user_errors_proto_goTypes,
		DependencyIndexes: file_errcodes_user_errors_proto_depIdxs,
		EnumInfos:         file_errcodes_user_errors_proto_enumTypes,
	}.Build()
	File_errcodes_user_errors_proto = out.File
	file_errcodes_user_errors_proto_goTypes = nil
	file_errcodes_user_errors_proto_depIdxs = nil
}
