// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package errcodes

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 用户未找到
func IsUserNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_NOT_FOUND.String() && e.Code == 404
}

// 用户未找到
func ErrorUserNotFoundf(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_USER_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 用户已存在
func IsUserAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_ALREADY_EXISTS.String() && e.Code == 409
}

// 用户已存在
func ErrorUserAlreadyExistsf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USER_ALREADY_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 无效的用户参数
func IsInvalidUserParams(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INVALID_USER_PARAMS.String() && e.Code == 400
}

// 无效的用户参数
func ErrorInvalidUserParamsf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_INVALID_USER_PARAMS.String(), fmt.Sprintf(format, args...))
}

// 租户ID无效
func IsInvalidTenantId(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INVALID_TENANT_ID.String() && e.Code == 400
}

// 租户ID无效
func ErrorInvalidTenantIdf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_INVALID_TENANT_ID.String(), fmt.Sprintf(format, args...))
}

// 用户名已被使用
func IsUsernameAlreadyTaken(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USERNAME_ALREADY_TAKEN.String() && e.Code == 409
}

// 用户名已被使用
func ErrorUsernameAlreadyTakenf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USERNAME_ALREADY_TAKEN.String(), fmt.Sprintf(format, args...))
}

// 年龄超出范围
func IsAgeOutOfRange(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_AGE_OUT_OF_RANGE.String() && e.Code == 400
}

// 年龄超出范围
func ErrorAgeOutOfRangef(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_AGE_OUT_OF_RANGE.String(), fmt.Sprintf(format, args...))
}

// 业务规则违反
func IsBusinessRuleViolation(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_BUSINESS_RULE_VIOLATION.String() && e.Code == 422
}

// 业务规则违反
func ErrorBusinessRuleViolationf(format string, args ...interface{}) *errors.Error {
	return errors.New(422, UserErrorReason_BUSINESS_RULE_VIOLATION.String(), fmt.Sprintf(format, args...))
}

// 用户邮箱已存在
func IsUserEmailExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_EMAIL_EXISTS.String() && e.Code == 409
}

// 用户邮箱已存在
func ErrorUserEmailExistsf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USER_EMAIL_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 用户手机号已存在
func IsUserPhoneExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_PHONE_EXISTS.String() && e.Code == 409
}

// 用户手机号已存在
func ErrorUserPhoneExistsf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USER_PHONE_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 权限被拒绝
func IsPermissionDenied(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PERMISSION_DENIED.String() && e.Code == 403
}

// 权限被拒绝
func ErrorPermissionDeniedf(format string, args ...interface{}) *errors.Error {
	return errors.New(403, UserErrorReason_PERMISSION_DENIED.String(), fmt.Sprintf(format, args...))
}

var (
	// 用户未找到
	ErrorUserNotFound *errors.Error
	// 用户已存在
	ErrorUserAlreadyExists *errors.Error
	// 无效的用户参数
	ErrorInvalidUserParams *errors.Error
	// 租户ID无效
	ErrorInvalidTenantId *errors.Error
	// 用户名已被使用
	ErrorUsernameAlreadyTaken *errors.Error
	// 年龄超出范围
	ErrorAgeOutOfRange *errors.Error
	// 业务规则违反
	ErrorBusinessRuleViolation *errors.Error
	// 用户邮箱已存在
	ErrorUserEmailExists *errors.Error
	// 用户手机号已存在
	ErrorUserPhoneExists *errors.Error
	// 权限被拒绝
	ErrorPermissionDenied *errors.Error
)

func init() {
	ErrorUserNotFound = ErrorUserNotFoundf("用户不存在")
	ErrorUserAlreadyExists = ErrorUserAlreadyExistsf("用户已存在")
	ErrorInvalidUserParams = ErrorInvalidUserParamsf("无效的用户参数")
	ErrorInvalidTenantId = ErrorInvalidTenantIdf("无效的租户ID")
	ErrorUsernameAlreadyTaken = ErrorUsernameAlreadyTakenf("用户名已被使用")
	ErrorAgeOutOfRange = ErrorAgeOutOfRangef("年龄超出有效范围")
	ErrorBusinessRuleViolation = ErrorBusinessRuleViolationf("业务规则违反")
	ErrorUserEmailExists = ErrorUserEmailExistsf("邮箱已被使用")
	ErrorUserPhoneExists = ErrorUserPhoneExistsf("手机号已被使用")
	ErrorPermissionDenied = ErrorPermissionDeniedf("权限不足")
}
