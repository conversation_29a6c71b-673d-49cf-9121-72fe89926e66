// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package errcodes

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 未指定错误
func IsErrorReasonUnspecified(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_ERROR_REASON_UNSPECIFIED.String() && e.Code == 500
}

// 未指定错误
func ErrorErrorReasonUnspecifiedf(format string, args ...interface{}) *errors.Error {
	return errors.New(500, CommonErrorReason_ERROR_REASON_UNSPECIFIED.String(), fmt.Sprintf(format, args...))
}

// 系统错误
func IsSystemError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_SYSTEM_ERROR.String() && e.Code == 500
}

// 系统错误
func ErrorSystemErrorf(format string, args ...interface{}) *errors.Error {
	return errors.New(500, CommonErrorReason_SYSTEM_ERROR.String(), fmt.Sprintf(format, args...))
}

// 参数验证错误
func IsInvalidParameter(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_INVALID_PARAMETER.String() && e.Code == 400
}

// 参数验证错误
func ErrorInvalidParameterf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, CommonErrorReason_INVALID_PARAMETER.String(), fmt.Sprintf(format, args...))
}

var (
	// 未指定错误
	ErrorErrorReasonUnspecified *errors.Error
	// 系统错误
	ErrorSystemError *errors.Error
	// 参数验证错误
	ErrorInvalidParameter *errors.Error
)

func init() {
	ErrorErrorReasonUnspecified = ErrorErrorReasonUnspecifiedf("未知错误")
	ErrorSystemError = ErrorSystemErrorf("系统内部错误")
	ErrorInvalidParameter = ErrorInvalidParameterf("无效的参数")
}
