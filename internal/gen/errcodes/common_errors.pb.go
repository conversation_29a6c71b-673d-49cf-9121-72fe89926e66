// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: errcodes/common_errors.proto

package errcodes

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CommonErrorReason 定义通用错误码
type CommonErrorReason int32

const (
	// 未指定错误
	CommonErrorReason_ERROR_REASON_UNSPECIFIED CommonErrorReason = 0
	// 系统错误
	CommonErrorReason_SYSTEM_ERROR CommonErrorReason = 1
	// 参数验证错误
	CommonErrorReason_INVALID_PARAMETER CommonErrorReason = 4
)

// Enum value maps for CommonErrorReason.
var (
	CommonErrorReason_name = map[int32]string{
		0: "ERROR_REASON_UNSPECIFIED",
		1: "SYSTEM_ERROR",
		4: "INVALID_PARAMETER",
	}
	CommonErrorReason_value = map[string]int32{
		"ERROR_REASON_UNSPECIFIED": 0,
		"SYSTEM_ERROR":             1,
		"INVALID_PARAMETER":        4,
	}
)

func (x CommonErrorReason) Enum() *CommonErrorReason {
	p := new(CommonErrorReason)
	*p = x
	return p
}

func (x CommonErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_errcodes_common_errors_proto_enumTypes[0].Descriptor()
}

func (CommonErrorReason) Type() protoreflect.EnumType {
	return &file_errcodes_common_errors_proto_enumTypes[0]
}

func (x CommonErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonErrorReason.Descriptor instead.
func (CommonErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_errcodes_common_errors_proto_rawDescGZIP(), []int{0}
}

var File_errcodes_common_errors_proto protoreflect.FileDescriptor

const file_errcodes_common_errors_proto_rawDesc = "" +
	"\n" +
	"\x1cerrcodes/common_errors.proto\x12\berrcodes\x1a\x13errors/errors.proto*\xa8\x01\n" +
	"\x11CommonErrorReason\x121\n" +
	"\x18ERROR_REASON_UNSPECIFIED\x10\x00\x1a\x13\xa8E\xf4\x03\xb2E\f未知错误\x12+\n" +
	"\fSYSTEM_ERROR\x10\x01\x1a\x19\xa8E\xf4\x03\xb2E\x12系统内部错误\x12-\n" +
	"\x11INVALID_PARAMETER\x10\x04\x1a\x16\xa8E\x90\x03\xb2E\x0f无效的参数\x1a\x04\xa0E\xf4\x03BCZAgithub.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodesb\x06proto3"

var (
	file_errcodes_common_errors_proto_rawDescOnce sync.Once
	file_errcodes_common_errors_proto_rawDescData []byte
)

func file_errcodes_common_errors_proto_rawDescGZIP() []byte {
	file_errcodes_common_errors_proto_rawDescOnce.Do(func() {
		file_errcodes_common_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errcodes_common_errors_proto_rawDesc), len(file_errcodes_common_errors_proto_rawDesc)))
	})
	return file_errcodes_common_errors_proto_rawDescData
}

var file_errcodes_common_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errcodes_common_errors_proto_goTypes = []any{
	(CommonErrorReason)(0), // 0: errcodes.CommonErrorReason
}
var file_errcodes_common_errors_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_errcodes_common_errors_proto_init() }
func file_errcodes_common_errors_proto_init() {
	if File_errcodes_common_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errcodes_common_errors_proto_rawDesc), len(file_errcodes_common_errors_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errcodes_common_errors_proto_goTypes,
		DependencyIndexes: file_errcodes_common_errors_proto_depIdxs,
		EnumInfos:         file_errcodes_common_errors_proto_enumTypes,
	}.Build()
	File_errcodes_common_errors_proto = out.File
	file_errcodes_common_errors_proto_goTypes = nil
	file_errcodes_common_errors_proto_depIdxs = nil
}
