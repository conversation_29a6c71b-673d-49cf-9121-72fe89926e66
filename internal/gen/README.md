# Generated Code Directory

Auto-generated code from various tools. **DO NOT EDIT** these files directly.

## Business Context

- **Multi-tenant architecture**: All entities include `tenant_id` for data isolation
- **Error handling**: Custom error codes with i18n support via `errcodes/`
- **API validation**: Uses buf.validate with Chinese regex patterns for user inputs

## Key Patterns

- **Ent ORM**: Database schema in `../data/schema/` → generates `database/ent/`
- **Proto APIs**: API definitions in `../../../api/` → generates language-specific clients
- **Error codes**: Error definitions in `../../../protos/errcodes/` → generates `errcodes/`

## Custom Configurations

- Ent features: `--feature intercept` for middleware support
- Proto validation: Chinese name patterns, mobile number formats
- Multi-language error messages in error code generation

## Dependencies

- Changes to `../data/schema/*.go` require `go generate`
- API changes require `make api` (protoc + buf validate)
- Error definitions require `make errors` for regeneration
