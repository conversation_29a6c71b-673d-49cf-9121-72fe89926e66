package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

// MockTenantService 模拟租户服务实现
type MockTenantService struct {
	log *log.Helper
}

// NewMockTenantService 创建模拟租户服务实例
func NewMockTenantService(logger log.Logger) TenantService {
	return &MockTenantService{
		log: log.NewHelper(logger),
	}
}

// HasCreatePermission 检查租户是否有创建用户的权限（模拟实现）
func (s *MockTenantService) HasCreatePermission(ctx context.Context, tenantID uint64) bool {
	// 模拟业务逻辑：
	// 1. 租户ID为999999的是测试租户，不允许创建用户
	// 2. 租户ID为0的是系统租户，允许创建
	// 3. 其他租户都允许创建
	
	if tenantID == 999999 {
		s.log.Warnw("测试租户不允许创建用户", "tenant_id", tenantID)
		return false
	}
	
	s.log.Debugw("租户权限检查通过", "tenant_id", tenantID)
	return true
}

// MockEventBus 模拟事件总线实现
type MockEventBus struct {
	log *log.Helper
}

// NewMockEventBus 创建模拟事件总线实例
func NewMockEventBus(logger log.Logger) EventBus {
	return &MockEventBus{
		log: log.NewHelper(logger),
	}
}

// Publish 发布事件（模拟实现）
func (e *MockEventBus) Publish(ctx context.Context, event interface{}) error {
	// 模拟事件发布逻辑
	switch evt := event.(type) {
	case *UserCreated:
		e.log.Infow("用户创建事件已发布", 
			"event_type", "UserCreated",
			"user_id", evt.UserID,
			"tenant_id", evt.TenantID,
			"name", evt.Name,
		)
		
		// 这里可以添加实际的事件处理逻辑，比如：
		// 1. 发送欢迎邮件
		// 2. 创建用户默认配置
		// 3. 记录审计日志
		// 4. 通知相关系统
		
	default:
		e.log.Infow("未知事件类型", "event", event)
	}
	
	return nil
}
