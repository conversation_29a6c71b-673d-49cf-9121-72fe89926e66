package biz

import (
	"context"

	"github.com/go-kratos/kratos-layout/internal/domain"
	"github.com/go-kratos/kratos/v2/log"
)

// UserUsecase 用户用例
type UserUsecase struct {
	repo domain.UserRepo
	log  *log.Helper
}

var _ domain.UserUsecase = (*UserUsecase)(nil)

// NewUserUsecase 创建用户用例实例
func NewUserUsecase(repo domain.UserRepo, logger log.Logger) domain.UserUsecase {
	return &UserUsecase{repo: repo, log: log.NewHelper(logger)}
}

func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	// 验证用户数据
	if err := u.Validate(); err != nil {
		return nil, err
	}

	// 创建用户
	return uc.repo.Create(ctx, u)
}

func (uc *UserUsecase) Update(ctx context.Context, u *domain.User) error {
	// 验证用户数据
	if err := u.Validate(); err != nil {
		return err
	}

	// 更新用户
	return uc.repo.Update(ctx, u)
}

func (uc *UserUsecase) Delete(ctx context.Context, id uint64) error {
	return uc.repo.Delete(ctx, id)
}

func (uc *UserUsecase) Get(ctx context.Context, id uint64) (*domain.User, error) {
	return uc.repo.Get(ctx, id)
}

func (uc *UserUsecase) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	// 参数校验已在 proto 和 service 层完成，这里主要处理业务逻辑
	// 可以添加业务规则，如数据权限等
	
	// 查询用户列表
	return uc.repo.List(ctx, pageNum, pageSize)
}
