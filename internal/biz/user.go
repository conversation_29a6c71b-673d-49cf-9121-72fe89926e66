package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos-layout/internal/domain"
	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
	"github.com/go-kratos/kratos/v2/log"
)

// TenantService 租户服务接口（模拟）
type TenantService interface {
	HasCreatePermission(ctx context.Context, tenantID uint64) bool
}

// EventBus 事件总线接口（模拟）
type EventBus interface {
	Publish(ctx context.Context, event interface{}) error
}

// UserCreated 用户创建事件
type UserCreated struct {
	UserID   uint64
	TenantID uint64
	Name     string
}

// UserUsecase 用户用例
type UserUsecase struct {
	repo          domain.UserRepo
	tenantService TenantService
	eventBus      EventBus
	log           *log.Helper
}

var _ domain.UserUsecase = (*UserUsecase)(nil)

// NewUserUsecase 创建用户用例实例
func NewUserUsecase(repo domain.UserRepo, tenantService TenantService, eventBus EventBus, logger log.Logger) domain.UserUsecase {
	return &UserUsecase{
		repo:          repo,
		tenantService: tenantService,
		eventBus:      eventBus,
		log:           log.NewHelper(logger),
	}
}

func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	// 1. 领域对象校验（复杂业务规则）
	if err := u.Validate(); err != nil {
		uc.log.Warnw("用户业务规则校验失败", "error", err, "user", u.Name)
		return nil, err
	}

	// 2. 业务流程校验：邮箱唯一性检查
	if u.Email != "" {
		exists, err := uc.repo.ExistsByEmail(ctx, u.Email)
		if err != nil {
			uc.log.Errorw("检查邮箱唯一性失败", "error", err, "email", u.Email)
			return nil, err
		}
		if exists {
			return nil, ec.ErrorUserEmailExists.WithMetadata(map[string]string{
				"email": u.Email,
			})
		}
	}

	// 3. 业务流程校验：手机号唯一性检查
	if u.Phone != "" {
		exists, err := uc.repo.ExistsByPhone(ctx, u.Phone)
		if err != nil {
			uc.log.Errorw("检查手机号唯一性失败", "error", err, "phone", u.Phone)
			return nil, err
		}
		if exists {
			return nil, ec.ErrorUserPhoneExists.WithMetadata(map[string]string{
				"phone": u.Phone,
			})
		}
	}

	// 4. 租户权限校验（模拟）
	if u.TenantID > 0 && uc.tenantService != nil {
		if !uc.tenantService.HasCreatePermission(ctx, u.TenantID) {
			return nil, ec.ErrorPermissionDenied.WithMetadata(map[string]string{
				"tenant_id": fmt.Sprintf("%d", u.TenantID),
				"action":    "create_user",
			})
		}
	}

	// 5. 设置创建时间
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now

	// 6. 执行创建
	createdUser, err := uc.repo.Create(ctx, u)
	if err != nil {
		uc.log.Errorw("创建用户失败", "error", err, "user", u.Name)
		return nil, err
	}

	// 7. 业务事件发布（模拟）
	if uc.eventBus != nil {
		event := &UserCreated{
			UserID:   createdUser.ID,
			TenantID: createdUser.TenantID,
			Name:     createdUser.Name,
		}
		if err := uc.eventBus.Publish(ctx, event); err != nil {
			uc.log.Warnw("发布用户创建事件失败", "error", err, "user_id", createdUser.ID)
			// 事件发布失败不影响用户创建
		}
	}

	uc.log.Infow("用户创建成功", "user_id", createdUser.ID, "name", createdUser.Name)
	return createdUser, nil
}

func (uc *UserUsecase) Update(ctx context.Context, u *domain.User) error {
	// 验证用户数据
	if err := u.Validate(); err != nil {
		return err
	}

	// 更新用户
	return uc.repo.Update(ctx, u)
}

func (uc *UserUsecase) Delete(ctx context.Context, id uint64) error {
	return uc.repo.Delete(ctx, id)
}

func (uc *UserUsecase) Get(ctx context.Context, id uint64) (*domain.User, error) {
	return uc.repo.Get(ctx, id)
}

func (uc *UserUsecase) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	// 参数校验已在 proto 和 service 层完成，这里主要处理业务逻辑
	// 可以添加业务规则，如数据权限等

	// 查询用户列表
	return uc.repo.List(ctx, pageNum, pageSize)
}
