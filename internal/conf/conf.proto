syntax = "proto3";
package kratos.api;

option go_package = "github.com/go-kratos/kratos-layout/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

enum RunMode {
  // 开发模式
  DEV = 0;
  // 生产模式
  PROD = 1;
}

enum MigrateMode {
  // 不允许修改表
  DISABLED = 0;
  // 只允许追加字段
  APPEND = 1; 
  // 允许修改+删除
  UNLIMITED = 2;
}

message Data {
  // 运行模式
  RunMode run_mode = 1;
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration read_timeout = 3;
    google.protobuf.Duration write_timeout = 4;
  }
  Database database = 2;
  Redis redis = 3;
  // 是否开启多租户
  bool tenant_enabled = 4;
  // 低代码配置
  message DjConfig {
    // 指定分页size最大值
    uint64 max_page_size = 1;
    // 默认不返回fields,必须明确指定fields才会返回,不支持*
    bool specific_fields_required = 2;
    // migrate_mode
    MigrateMode migrate_mode = 3;
  }
  DjConfig dj_config = 5;
}
