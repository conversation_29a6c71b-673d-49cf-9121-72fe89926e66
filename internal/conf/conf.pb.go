// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RunMode int32

const (
	// 开发模式
	RunMode_DEV RunMode = 0
	// 生产模式
	RunMode_PROD RunMode = 1
)

// Enum value maps for RunMode.
var (
	RunMode_name = map[int32]string{
		0: "DEV",
		1: "PROD",
	}
	RunMode_value = map[string]int32{
		"DEV":  0,
		"PROD": 1,
	}
)

func (x RunMode) Enum() *RunMode {
	p := new(RunMode)
	*p = x
	return p
}

func (x RunMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunMode) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_conf_proto_enumTypes[0].Descriptor()
}

func (RunMode) Type() protoreflect.EnumType {
	return &file_conf_conf_proto_enumTypes[0]
}

func (x RunMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunMode.Descriptor instead.
func (RunMode) EnumDescriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

type MigrateMode int32

const (
	// 不允许修改表
	MigrateMode_DISABLED MigrateMode = 0
	// 只允许追加字段
	MigrateMode_APPEND MigrateMode = 1
	// 允许修改+删除
	MigrateMode_UNLIMITED MigrateMode = 2
)

// Enum value maps for MigrateMode.
var (
	MigrateMode_name = map[int32]string{
		0: "DISABLED",
		1: "APPEND",
		2: "UNLIMITED",
	}
	MigrateMode_value = map[string]int32{
		"DISABLED":  0,
		"APPEND":    1,
		"UNLIMITED": 2,
	}
)

func (x MigrateMode) Enum() *MigrateMode {
	p := new(MigrateMode)
	*p = x
	return p
}

func (x MigrateMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MigrateMode) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_conf_proto_enumTypes[1].Descriptor()
}

func (MigrateMode) Type() protoreflect.EnumType {
	return &file_conf_conf_proto_enumTypes[1]
}

func (x MigrateMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MigrateMode.Descriptor instead.
func (MigrateMode) EnumDescriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1}
}

type Bootstrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_conf_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Http          *Server_HTTP           `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc          *Server_GRPC           `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_conf_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

type Data struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 运行模式
	RunMode  RunMode        `protobuf:"varint,1,opt,name=run_mode,json=runMode,proto3,enum=kratos.api.RunMode" json:"run_mode,omitempty"`
	Database *Data_Database `protobuf:"bytes,2,opt,name=database,proto3" json:"database,omitempty"`
	Redis    *Data_Redis    `protobuf:"bytes,3,opt,name=redis,proto3" json:"redis,omitempty"`
	// 是否开启多租户
	TenantEnabled bool           `protobuf:"varint,4,opt,name=tenant_enabled,json=tenantEnabled,proto3" json:"tenant_enabled,omitempty"`
	DjConfig      *Data_DjConfig `protobuf:"bytes,5,opt,name=dj_config,json=djConfig,proto3" json:"dj_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_conf_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Data) GetRunMode() RunMode {
	if x != nil {
		return x.RunMode
	}
	return RunMode_DEV
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetTenantEnabled() bool {
	if x != nil {
		return x.TenantEnabled
	}
	return false
}

func (x *Data) GetDjConfig() *Data_DjConfig {
	if x != nil {
		return x.DjConfig
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_conf_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_conf_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Data_Database struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Driver        string                 `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	mi := &file_conf_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type Data_Redis struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	ReadTimeout   *durationpb.Duration   `protobuf:"bytes,3,opt,name=read_timeout,json=readTimeout,proto3" json:"read_timeout,omitempty"`
	WriteTimeout  *durationpb.Duration   `protobuf:"bytes,4,opt,name=write_timeout,json=writeTimeout,proto3" json:"write_timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	mi := &file_conf_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Data_Redis) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetReadTimeout() *durationpb.Duration {
	if x != nil {
		return x.ReadTimeout
	}
	return nil
}

func (x *Data_Redis) GetWriteTimeout() *durationpb.Duration {
	if x != nil {
		return x.WriteTimeout
	}
	return nil
}

// 低代码配置
type Data_DjConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 指定分页size最大值
	MaxPageSize uint64 `protobuf:"varint,1,opt,name=max_page_size,json=maxPageSize,proto3" json:"max_page_size,omitempty"`
	// 默认不返回fields,必须明确指定fields才会返回,不支持*
	SpecificFieldsRequired bool `protobuf:"varint,2,opt,name=specific_fields_required,json=specificFieldsRequired,proto3" json:"specific_fields_required,omitempty"`
	// migrate_mode
	MigrateMode   MigrateMode `protobuf:"varint,3,opt,name=migrate_mode,json=migrateMode,proto3,enum=kratos.api.MigrateMode" json:"migrate_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_DjConfig) Reset() {
	*x = Data_DjConfig{}
	mi := &file_conf_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_DjConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_DjConfig) ProtoMessage() {}

func (x *Data_DjConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_DjConfig.ProtoReflect.Descriptor instead.
func (*Data_DjConfig) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Data_DjConfig) GetMaxPageSize() uint64 {
	if x != nil {
		return x.MaxPageSize
	}
	return 0
}

func (x *Data_DjConfig) GetSpecificFieldsRequired() bool {
	if x != nil {
		return x.SpecificFieldsRequired
	}
	return false
}

func (x *Data_DjConfig) GetMigrateMode() MigrateMode {
	if x != nil {
		return x.MigrateMode
	}
	return MigrateMode_DISABLED
}

var File_conf_conf_proto protoreflect.FileDescriptor

const file_conf_conf_proto_rawDesc = "" +
	"\n" +
	"\x0fconf/conf.proto\x12\n" +
	"kratos.api\x1a\x1egoogle/protobuf/duration.proto\"]\n" +
	"\tBootstrap\x12*\n" +
	"\x06server\x18\x01 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x02 \x01(\v2\x10.kratos.api.DataR\x04data\"\xb8\x02\n" +
	"\x06Server\x12+\n" +
	"\x04http\x18\x01 \x01(\v2\x17.kratos.api.Server.HTTPR\x04http\x12+\n" +
	"\x04grpc\x18\x02 \x01(\v2\x17.kratos.api.Server.GRPCR\x04grpc\x1ai\n" +
	"\x04HTTP\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1ai\n" +
	"\x04GRPC\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\"\x93\x05\n" +
	"\x04Data\x12.\n" +
	"\brun_mode\x18\x01 \x01(\x0e2\x13.kratos.api.RunModeR\arunMode\x125\n" +
	"\bdatabase\x18\x02 \x01(\v2\x19.kratos.api.Data.DatabaseR\bdatabase\x12,\n" +
	"\x05redis\x18\x03 \x01(\v2\x16.kratos.api.Data.RedisR\x05redis\x12%\n" +
	"\x0etenant_enabled\x18\x04 \x01(\bR\rtenantEnabled\x126\n" +
	"\tdj_config\x18\x05 \x01(\v2\x19.kratos.api.Data.DjConfigR\bdjConfig\x1a:\n" +
	"\bDatabase\x12\x16\n" +
	"\x06driver\x18\x01 \x01(\tR\x06driver\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x1a\xb3\x01\n" +
	"\x05Redis\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x12<\n" +
	"\fread_timeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\vreadTimeout\x12>\n" +
	"\rwrite_timeout\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\fwriteTimeout\x1a\xa4\x01\n" +
	"\bDjConfig\x12\"\n" +
	"\rmax_page_size\x18\x01 \x01(\x04R\vmaxPageSize\x128\n" +
	"\x18specific_fields_required\x18\x02 \x01(\bR\x16specificFieldsRequired\x12:\n" +
	"\fmigrate_mode\x18\x03 \x01(\x0e2\x17.kratos.api.MigrateModeR\vmigrateMode*\x1c\n" +
	"\aRunMode\x12\a\n" +
	"\x03DEV\x10\x00\x12\b\n" +
	"\x04PROD\x10\x01*6\n" +
	"\vMigrateMode\x12\f\n" +
	"\bDISABLED\x10\x00\x12\n" +
	"\n" +
	"\x06APPEND\x10\x01\x12\r\n" +
	"\tUNLIMITED\x10\x02B7Z5github.com/go-kratos/kratos-layout/internal/conf;confb\x06proto3"

var (
	file_conf_conf_proto_rawDescOnce sync.Once
	file_conf_conf_proto_rawDescData []byte
)

func file_conf_conf_proto_rawDescGZIP() []byte {
	file_conf_conf_proto_rawDescOnce.Do(func() {
		file_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_conf_conf_proto_rawDesc), len(file_conf_conf_proto_rawDesc)))
	})
	return file_conf_conf_proto_rawDescData
}

var file_conf_conf_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_conf_conf_proto_goTypes = []any{
	(RunMode)(0),                // 0: kratos.api.RunMode
	(MigrateMode)(0),            // 1: kratos.api.MigrateMode
	(*Bootstrap)(nil),           // 2: kratos.api.Bootstrap
	(*Server)(nil),              // 3: kratos.api.Server
	(*Data)(nil),                // 4: kratos.api.Data
	(*Server_HTTP)(nil),         // 5: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),         // 6: kratos.api.Server.GRPC
	(*Data_Database)(nil),       // 7: kratos.api.Data.Database
	(*Data_Redis)(nil),          // 8: kratos.api.Data.Redis
	(*Data_DjConfig)(nil),       // 9: kratos.api.Data.DjConfig
	(*durationpb.Duration)(nil), // 10: google.protobuf.Duration
}
var file_conf_conf_proto_depIdxs = []int32{
	3,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	4,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	5,  // 2: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	6,  // 3: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	0,  // 4: kratos.api.Data.run_mode:type_name -> kratos.api.RunMode
	7,  // 5: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	8,  // 6: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	9,  // 7: kratos.api.Data.dj_config:type_name -> kratos.api.Data.DjConfig
	10, // 8: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	10, // 9: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	10, // 10: kratos.api.Data.Redis.read_timeout:type_name -> google.protobuf.Duration
	10, // 11: kratos.api.Data.Redis.write_timeout:type_name -> google.protobuf.Duration
	1,  // 12: kratos.api.Data.DjConfig.migrate_mode:type_name -> kratos.api.MigrateMode
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_conf_conf_proto_init() }
func file_conf_conf_proto_init() {
	if File_conf_conf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_conf_conf_proto_rawDesc), len(file_conf_conf_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_conf_proto_goTypes,
		DependencyIndexes: file_conf_conf_proto_depIdxs,
		EnumInfos:         file_conf_conf_proto_enumTypes,
		MessageInfos:      file_conf_conf_proto_msgTypes,
	}.Build()
	File_conf_conf_proto = out.File
	file_conf_conf_proto_goTypes = nil
	file_conf_conf_proto_depIdxs = nil
}
