package service

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos-layout/internal/domain"
	userv1 "github.com/go-kratos/kratos-layout/internal/gen/api/user/v1"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockUserUsecase 模拟用户用例
type MockUserUsecase struct {
	mock.Mock
}

func (m *MockUserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	args := m.Called(ctx, u)
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockUserUsecase) Update(ctx context.Context, u *domain.User) error {
	args := m.Called(ctx, u)
	return args.Error(0)
}

func (m *MockUserUsecase) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserUsecase) Get(ctx context.Context, id uint64) (*domain.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockUserUsecase) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	args := m.Called(ctx, pageNum, pageSize)
	return args.Get(0).([]*domain.User), args.Int(1), args.Error(2)
}

func TestUserService_CreateUser(t *testing.T) {
	// 准备测试数据
	mockUC := new(MockUserUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewUserService(mockUC, logger)

	ctx := context.Background()
	req := &userv1.CreateUserRequest{
		Name:     "张三",
		Age:      25,
		TenantId: 1001,
		Email:    stringPtr("<EMAIL>"),
		Phone:    stringPtr("13800138000"),
		Role:     userv1.UserRole_USER_ROLE_USER,
	}

	expectedUser := &domain.User{
		ID:        1,
		Name:      "张三",
		Age:       25,
		TenantID:  1001,
		Email:     "<EMAIL>",
		Phone:     "13800138000",
		Role:      domain.UserRoleUser,
		Status:    domain.UserStatusActive,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 设置mock期望
	mockUC.On("Create", ctx, mock.MatchedBy(func(u *domain.User) bool {
		return u.Name == "张三" && u.Age == 25 && u.TenantID == 1001
	})).Return(expectedUser, nil)

	// 执行测试
	reply, err := service.CreateUser(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, reply)
	assert.Equal(t, uint64(1), reply.Id)

	// 验证mock调用
	mockUC.AssertExpectations(t)
}

func TestUserService_ListUsers(t *testing.T) {
	// 准备测试数据
	mockUC := new(MockUserUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewUserService(mockUC, logger)

	ctx := context.Background()
	req := &userv1.ListUsersRequest{
		PageSize: 10,
		PageNum:  1,
	}

	expectedUsers := []*domain.User{
		{
			ID:        1,
			Name:      "张三",
			Age:       25,
			TenantID:  1001,
			Email:     "<EMAIL>",
			Phone:     "13800138000",
			Role:      domain.UserRoleUser,
			Status:    domain.UserStatusActive,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:        2,
			Name:      "李四",
			Age:       30,
			TenantID:  1001,
			Email:     "<EMAIL>",
			Phone:     "13800138001",
			Role:      domain.UserRoleAdmin,
			Status:    domain.UserStatusActive,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// 设置mock期望
	mockUC.On("List", ctx, int32(1), int32(10)).Return(expectedUsers, 2, nil)

	// 执行测试
	reply, err := service.ListUsers(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, reply)
	assert.Equal(t, uint32(2), reply.Total)
	assert.Len(t, reply.Users, 2)

	// 验证第一个用户
	user1 := reply.Users[0]
	assert.Equal(t, uint64(1), user1.Id)
	assert.Equal(t, "张三", user1.Name)
	assert.Equal(t, int32(25), user1.Age)
	assert.Equal(t, "<EMAIL>", user1.Email)
	assert.Equal(t, userv1.UserRole_USER_ROLE_USER, user1.Role)
	assert.Equal(t, userv1.UserStatus_USER_STATUS_ACTIVE, user1.Status)

	// 验证第二个用户
	user2 := reply.Users[1]
	assert.Equal(t, uint64(2), user2.Id)
	assert.Equal(t, "李四", user2.Name)
	assert.Equal(t, int32(30), user2.Age)
	assert.Equal(t, "<EMAIL>", user2.Email)
	assert.Equal(t, userv1.UserRole_USER_ROLE_ADMIN, user2.Role)
	assert.Equal(t, userv1.UserStatus_USER_STATUS_ACTIVE, user2.Status)

	// 验证mock调用
	mockUC.AssertExpectations(t)
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

// 测试辅助方法
func TestConvertProtoRole(t *testing.T) {
	tests := []struct {
		name      string
		protoRole userv1.UserRole
		expected  domain.UserRole
	}{
		{"User", userv1.UserRole_USER_ROLE_USER, domain.UserRoleUser},
		{"Admin", userv1.UserRole_USER_ROLE_ADMIN, domain.UserRoleAdmin},
		{"SuperAdmin", userv1.UserRole_USER_ROLE_SUPER_ADMIN, domain.UserRoleSuperAdmin},
		{"Unspecified", userv1.UserRole_USER_ROLE_UNSPECIFIED, domain.UserRoleUser},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertProtoRole(tt.protoRole)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertDomainRole(t *testing.T) {
	tests := []struct {
		name       string
		domainRole domain.UserRole
		expected   userv1.UserRole
	}{
		{"User", domain.UserRoleUser, userv1.UserRole_USER_ROLE_USER},
		{"Admin", domain.UserRoleAdmin, userv1.UserRole_USER_ROLE_ADMIN},
		{"SuperAdmin", domain.UserRoleSuperAdmin, userv1.UserRole_USER_ROLE_SUPER_ADMIN},
		{"Unspecified", domain.UserRoleUnspecified, userv1.UserRole_USER_ROLE_USER},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertDomainRole(tt.domainRole)
			assert.Equal(t, tt.expected, result)
		})
	}
}
