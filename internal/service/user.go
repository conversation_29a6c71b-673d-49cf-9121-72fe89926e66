package service

import (
	"context"

	"github.com/go-kratos/kratos-layout/internal/domain"
	userv1 "github.com/go-kratos/kratos-layout/internal/gen/api/user/v1"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type UserService struct {
	userv1.UnimplementedUserServer

	uc  domain.UserUsecase
	log *log.Helper
}

func NewUserService(uc domain.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
	// 创建领域对象
	domainUser := &domain.User{
		Name:     req.Name,
		Age:      int(req.Age),
		TenantID: req.TenantId,
	}
	
	// 执行领域层业务校验
	if err := domainUser.Validate(); err != nil {
		return nil, err
	}
	
	user, err := s.uc.Create(ctx, domainUser)
	if err != nil {
		return nil, err
	}
	return &userv1.CreateUserReply{
		Id: user.ID,
	}, nil
}

func (s *UserService) UpdateUser(ctx context.Context, req *userv1.UpdateUserRequest) (*userv1.UpdateUserReply, error) {
	// proto 校验已在中间件层完成，这里只处理业务逻辑
	// 可以添加跨 RPC 的校验，如认证信息、权限检查等
	
	err := s.uc.Update(ctx, &domain.User{
		ID:   req.Id,
		Name: req.Name,
		Age:  int(req.Age),
	})
	if err != nil {
		return nil, err
	}

	return &userv1.UpdateUserReply{
		Success: true,
	}, nil
}

func (s *UserService) DeleteUser(ctx context.Context, req *userv1.DeleteUserRequest) (*emptypb.Empty, error) {
	// proto 校验已在中间件层完成，这里可以添加权限检查等业务逻辑
	
	err := s.uc.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *UserService) ListUsers(ctx context.Context, req *userv1.ListUsersRequest) (*userv1.ListUsersReply, error) {
	// proto 校验已在中间件层完成，这里可以添加权限检查等业务逻辑
	
	users, total, err := s.uc.List(ctx, int32(req.PageNum), int32(req.PageSize))
	if err != nil {
		return nil, err
	}

	reply := &userv1.ListUsersReply{
		Total: uint32(total),
		Users: make([]*userv1.UserInfo, 0, len(users)),
	}

	for _, u := range users {
		reply.Users = append(reply.Users, &userv1.UserInfo{
			Id:       u.ID,
			Name:     u.Name,
			Age:      int32(u.Age),
			TenantId: u.TenantID,
		})
	}

	return reply, nil
}
