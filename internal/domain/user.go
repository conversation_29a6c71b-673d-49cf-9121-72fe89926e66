package domain

import (
	"context"
	"fmt"
	"strings"
	"time"

	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
)

// UserRole 用户角色枚举
type UserRole int32

const (
	UserRoleUnspecified UserRole = 0
	UserRoleUser        UserRole = 1 // 普通用户
	UserRoleAdmin       UserRole = 2 // 管理员
	UserRoleSuperAdmin  UserRole = 3 // 超级管理员
)

func (r UserRole) String() string {
	switch r {
	case UserRoleUser:
		return "user"
	case UserRoleAdmin:
		return "admin"
	case UserRoleSuperAdmin:
		return "super_admin"
	default:
		return "unspecified"
	}
}

// UserStatus 用户状态枚举
type UserStatus int32

const (
	UserStatusUnspecified UserStatus = 0
	UserStatusActive      UserStatus = 1 // 活跃
	UserStatusInactive    UserStatus = 2 // 非活跃
	UserStatusSuspended   UserStatus = 3 // 暂停
	UserStatusDeleted     UserStatus = 4 // 已删除
)

func (s UserStatus) String() string {
	switch s {
	case UserStatusActive:
		return "active"
	case UserStatusInactive:
		return "inactive"
	case UserStatusSuspended:
		return "suspended"
	case UserStatusDeleted:
		return "deleted"
	default:
		return "unspecified"
	}
}

// User 领域模型
type User struct {
	ID            uint64
	Name          string
	Age           int
	TenantID      uint64
	Email         string
	Phone         string
	Role          UserRole
	Status        UserStatus
	CreatorId     uint64
	CreatedAt     time.Time
	UpdatedAt     time.Time
	LastLoginTime *time.Time
}

// UserRepository 用户仓储接口
type UserRepo interface {
	Create(context.Context, *User) (*User, error)
	Update(context.Context, *User) error
	Delete(context.Context, uint64) error
	Get(context.Context, uint64) (*User, error)
	List(context.Context, int32, int32) ([]*User, int, error)
	// 新增方法：检查邮箱是否存在
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	// 新增方法：检查手机号是否存在
	ExistsByPhone(ctx context.Context, phone string) (bool, error)
	// 新增方法：根据租户ID获取用户数量
	CountByTenantID(ctx context.Context, tenantID uint64) (int, error)
}

// UserUsecase 定义用例接口
type UserUsecase interface {
	Create(ctx context.Context, u *User) (*User, error)
	Update(ctx context.Context, u *User) error
	Delete(ctx context.Context, id uint64) error
	Get(ctx context.Context, id uint64) (*User, error)
	List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}

// Validate 域对象的业务规则校验（不同于 proto 格式校验）
func (u *User) Validate() error {
	// 基本的格式校验已在 proto 层完成，这里主要做业务规则校验

	// 1. 跨字段关联校验：未成年人不能担任管理员
	if u.Age < 18 && u.Role == UserRoleAdmin {
		return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
			"rule":   "未成年人不能担任管理员",
			"age":    fmt.Sprintf("%d", u.Age),
			"role":   u.Role.String(),
			"reason": "管理员角色需要年满18岁",
		})
	}

	// 2. 超级管理员必须年满21岁
	if u.Age < 21 && u.Role == UserRoleSuperAdmin {
		return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
			"rule":   "超级管理员必须年满21岁",
			"age":    fmt.Sprintf("%d", u.Age),
			"role":   u.Role.String(),
			"reason": "超级管理员角色需要年满21岁",
		})
	}

	// 3. 领域特定规则：超级管理员不能归属于特定租户
	if u.TenantID > 0 && u.Role == UserRoleSuperAdmin {
		return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
			"rule":      "超级管理员不能归属于特定租户",
			"tenant_id": fmt.Sprintf("%d", u.TenantID),
			"role":      u.Role.String(),
			"reason":    "超级管理员应该是全局角色",
		})
	}

	// 4. 状态转换规则：近期活跃用户不能被删除
	if u.Status == UserStatusDeleted && u.LastLoginTime != nil &&
		u.LastLoginTime.After(time.Now().AddDate(0, 0, -30)) {
		return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
			"rule":   "近30天内活跃的用户不能被删除",
			"status": u.Status.String(),
			"reason": "用户在30天内有登录记录，不允许删除",
		})
	}

	// 5. 敏感词汇检查（示例）
	if strings.Contains(strings.ToLower(u.Name), "admin") && u.Role != UserRoleAdmin && u.Role != UserRoleSuperAdmin {
		return ec.ErrorBusinessRuleViolation.WithMetadata(map[string]string{
			"rule":   "普通用户名不能包含管理员关键词",
			"name":   u.Name,
			"role":   u.Role.String(),
			"reason": "用户名包含敏感词汇",
		})
	}

	return nil
}

// CanModify 检查用户是否可以被指定操作者修改
func (u *User) CanModify(operatorID uint64, operatorRole UserRole, operatorTenantID uint64) bool {
	// 用户可以修改自己
	if u.ID == operatorID {
		return true
	}

	// 超级管理员可以修改任何用户
	if operatorRole == UserRoleSuperAdmin {
		return true
	}

	// 管理员可以修改同租户的普通用户
	if operatorRole == UserRoleAdmin && operatorTenantID == u.TenantID && u.Role == UserRoleUser {
		return true
	}

	return false
}

// CanDelete 检查用户是否可以被删除
func (u *User) CanDelete(operatorID uint64, operatorRole UserRole, operatorTenantID uint64) bool {
	// 用户不能删除自己
	if u.ID == operatorID {
		return false
	}

	// 已删除的用户不能再次删除
	if u.Status == UserStatusDeleted {
		return false
	}

	// 超级管理员可以删除除自己外的任何用户
	if operatorRole == UserRoleSuperAdmin {
		return true
	}

	// 管理员可以删除同租户的普通用户
	if operatorRole == UserRoleAdmin && operatorTenantID == u.TenantID && u.Role == UserRoleUser {
		return true
	}

	return false
}

// IsActive 检查用户是否处于活跃状态
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// HasRole 检查用户是否具有指定角色
func (u *User) HasRole(role UserRole) bool {
	return u.Role == role
}

// IsAdminOrAbove 检查用户是否是管理员或更高级别
func (u *User) IsAdminOrAbove() bool {
	return u.Role == UserRoleAdmin || u.Role == UserRoleSuperAdmin
}
