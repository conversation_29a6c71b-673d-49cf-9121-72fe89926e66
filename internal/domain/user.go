package domain

import (
	"context"

	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
)

// User 领域模型
type User struct {
	ID       uint64
	Name     string
	Age      int
	TenantID uint64
}

// UserRepository 用户仓储接口
type UserRepo interface {
	Create(context.Context, *User) (*User, error)
	Update(context.Context, *User) error
	Delete(context.Context, uint64) error
	Get(context.Context, uint64) (*User, error)
	List(context.Context, int32, int32) ([]*User, int, error)
}

// UserUsecase 定义用例接口
type UserUsecase interface {
	Create(ctx context.Context, u *User) (*User, error)
	Update(ctx context.Context, u *User) error
	Delete(ctx context.Context, id uint64) error
	Get(ctx context.Context, id uint64) (*User, error)
	List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}

// Validate 域对象的业务规则校验（不同于 proto 格式校验）
func (u *User) Validate() error {
	// 基本的格式校验已在 proto 层完成，这里主要做业务规则校验
	
	// 租户 ID 的领域校验：检查租户是否有效
	if u.TenantID <= 0 {
		return ec.ErrorInvalidTenantId.WithMetadata(map[string]string{
			"field": "tenant_id",
			"reason": "租户ID必须大于0",
		})
	}
	
	// 业务规则校验示例：
	// 1. VIP 用户名字不能包含敏感词汇
	// 2. 特定年龄段的用户需要额外的验证
	// 3. 特殊租户的用户名规则
	
	// 特殊年龄段的业务规则校验
	if u.Age > 65 {
		// 老年用户的特殊业务规则校验
		// 例如：需要监护人同意等
		// 这里可以添加更复杂的业务逻辑
	}
	
	// 租户特定的业务规则
	if u.TenantID == 999999 {
		// 测试租户的特殊限制
		// 例如：测试租户不能创建真实用户
	}
	
	return nil
}
