package internal

import (
	"fmt"

	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
)

// TestErrorFunctions demonstrates the generated error functions
func TestErrorFunctions() {
	// Test predefined error variables
	err1 := ec.ErrorUserNotFound
	fmt.Println("IsUserNotFound(ErrorUserNotFound):", ec.IsUserNotFound(err1))

	// Test formatted error creation functions
	err2 := ec.ErrorUserNotFoundf("用户ID %d 不存在", 123)
	fmt.Println("IsUserNotFound(ErrorUserNotFoundf):", ec.IsUserNotFound(err2))

	// Test error message
	fmt.Println("ErrorUserNotFoundf message:", err2.Error())

	// Test common errors
	err3 := ec.ErrorSystemError
	fmt.Println("IsSystemError(ErrorSystemError):", ec.IsSystemError(err3))

	// Test formatted common error
	err4 := ec.ErrorSystemErrorf("系统错误: %s", "数据库连接失败")
	fmt.Println("IsSystemError(ErrorSystemErrorf):", ec.IsSystemError(err4))

	// Print the errors for visual inspection
	fmt.Println("ErrorUserNotFound:", err1)
	fmt.Println("ErrorUserNotFoundf:", err2)
	fmt.Println("ErrorSystemError:", err3)
	fmt.Println("ErrorSystemErrorf:", err4)
}
