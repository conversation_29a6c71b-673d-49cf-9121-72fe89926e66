package data

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos-layout/internal/domain"
	"github.com/go-kratos/kratos-layout/internal/gen/database/ent"
	"github.com/go-kratos/kratos-layout/internal/gen/database/ent/user"
	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
	"github.com/go-kratos/kratos/v2/log"
)

// userRepo 实现 domain.UserRepo 接口
type userRepo struct {
	data *Data
	log  *log.Helper
}

// NewUserRepo 创建用户仓储实例
func NewUserRepo(data *Data, logger log.Logger) domain.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// Create 创建用户
func (r *userRepo) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	// 检查用户名是否已存在
	exists, err := r.data.db.User.Query().Where(user.NameEQ(u.Name)).Exist(ctx)
	if err != nil {
		return nil, ec.ErrorSystemErrorf("数据库查询失败").WithCause(err)
	}
	if exists {
		return nil, ec.ErrorUsernameAlreadyTaken.WithMetadata(map[string]string{
			"username": u.Name,
		})
	}

	// 创建用户
	create := r.data.db.User.Create().
		SetAge(u.Age).
		SetName(u.Name).
		SetTenantID(u.TenantID).
		SetRole(int32(u.Role)).
		SetStatus(int32(u.Status)).
		SetCreatedAt(u.CreatedAt).
		SetUpdatedAt(u.UpdatedAt)

	// 设置可选字段
	if u.Email != "" {
		create = create.SetEmail(u.Email)
	}
	if u.Phone != "" {
		create = create.SetPhone(u.Phone)
	}
	if u.CreatorId > 0 {
		create = create.SetCreatorID(u.CreatorId)
	}
	if u.LastLoginTime != nil {
		create = create.SetLastLoginTime(*u.LastLoginTime)
	}

	po, err := create.Save(ctx)
	if err != nil {
		if ent.IsConstraintError(err) {
			return nil, ec.ErrorUserAlreadyExists.WithCause(err)
		}
		return nil, ec.ErrorSystemErrorf("创建用户失败").WithCause(err)
	}
	return &domain.User{
		ID:            uint64(po.ID),
		Name:          po.Name,
		Age:           po.Age,
		TenantID:      po.TenantID,
		Email:         po.Email,
		Phone:         po.Phone,
		Role:          domain.UserRole(po.Role),
		Status:        domain.UserStatus(po.Status),
		CreatorId:     po.CreatorID,
		CreatedAt:     po.CreatedAt,
		UpdatedAt:     po.UpdatedAt,
		LastLoginTime: &po.LastLoginTime,
	}, nil
}

// Update 更新用户
func (r *userRepo) Update(ctx context.Context, u *domain.User) error {
	// 检查用户是否存在
	exists, err := r.data.db.User.Query().Where(user.IDEQ(int(u.ID))).Exist(ctx)
	if err != nil {
		return ec.ErrorSystemErrorf("查询用户失败").WithCause(err)
	}
	if !exists {
		return ec.ErrorUserNotFound.WithMetadata(map[string]string{
			"user_id": fmt.Sprintf("%d", u.ID),
		})
	}

	// 检查用户名是否已被其他用户使用
	existingUser, err := r.data.db.User.Query().Where(user.NameEQ(u.Name)).Only(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return ec.ErrorSystemErrorf("查询用户名失败").WithCause(err)
	}
	if existingUser != nil && existingUser.ID != int(u.ID) {
		return ec.ErrorUsernameAlreadyTaken.WithMetadata(map[string]string{
			"username": u.Name,
		})
	}

	// 更新用户
	_, err = r.data.db.User.UpdateOneID(int(u.ID)).
		SetAge(u.Age).
		SetName(u.Name).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return ec.ErrorUserNotFound.WithMetadata(map[string]string{
				"user_id": fmt.Sprintf("%d", u.ID),
			}).WithCause(err)
		}
		return ec.ErrorSystemErrorf("更新用户失败").WithCause(err)
	}
	return nil
}

// Delete 删除用户
func (r *userRepo) Delete(ctx context.Context, id uint64) error {
	err := r.data.db.User.DeleteOneID(int(id)).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return ec.ErrorUserNotFound.WithMetadata(map[string]string{
				"user_id": fmt.Sprintf("%d", id),
			}).WithCause(err)
		}
		return ec.ErrorSystemErrorf("删除用户失败").WithCause(err)
	}
	return nil
}

// Get 获取用户
func (r *userRepo) Get(ctx context.Context, id uint64) (*domain.User, error) {
	po, err := r.data.db.User.Get(ctx, int(id))
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, ec.ErrorUserNotFound.WithMetadata(map[string]string{
				"user_id": fmt.Sprintf("%d", id),
			}).WithCause(err)
		}
		return nil, ec.ErrorSystemErrorf("获取用户失败").WithCause(err)
	}

	return &domain.User{
		ID:            uint64(po.ID),
		Name:          po.Name,
		Age:           po.Age,
		TenantID:      po.TenantID,
		Email:         po.Email,
		Phone:         po.Phone,
		Role:          domain.UserRole(po.Role),
		Status:        domain.UserStatus(po.Status),
		CreatorId:     po.CreatorID,
		CreatedAt:     po.CreatedAt,
		UpdatedAt:     po.UpdatedAt,
		LastLoginTime: &po.LastLoginTime,
	}, nil
}

// List 获取用户列表
func (r *userRepo) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	// 获取总数
	total, err := r.data.db.User.Query().Count(ctx)
	if err != nil {
		return nil, 0, ec.ErrorSystemErrorf("获取用户总数失败").WithCause(err)
	}

	// 分页查询
	offset := (int(pageNum) - 1) * int(pageSize)
	pos, err := r.data.db.User.Query().
		Offset(offset).
		Limit(int(pageSize)).
		All(ctx)
	if err != nil {
		return nil, 0, ec.ErrorSystemErrorf("获取用户列表失败").WithCause(err)
	}

	// 转换为领域模型
	var users []*domain.User
	for _, po := range pos {
		users = append(users, &domain.User{
			ID:            uint64(po.ID),
			Name:          po.Name,
			Age:           po.Age,
			TenantID:      po.TenantID,
			Email:         po.Email,
			Phone:         po.Phone,
			Role:          domain.UserRole(po.Role),
			Status:        domain.UserStatus(po.Status),
			CreatorId:     po.CreatorID,
			CreatedAt:     po.CreatedAt,
			UpdatedAt:     po.UpdatedAt,
			LastLoginTime: &po.LastLoginTime,
		})
	}
	return users, total, nil
}

// ExistsByEmail 检查邮箱是否已存在
func (r *userRepo) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	if email == "" {
		return false, nil
	}

	exists, err := r.data.db.User.Query().Where(user.EmailEQ(email)).Exist(ctx)
	if err != nil {
		return false, ec.ErrorSystemErrorf("检查邮箱唯一性失败").WithCause(err)
	}
	return exists, nil
}

// ExistsByPhone 检查手机号是否已存在
func (r *userRepo) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	if phone == "" {
		return false, nil
	}

	exists, err := r.data.db.User.Query().Where(user.PhoneEQ(phone)).Exist(ctx)
	if err != nil {
		return false, ec.ErrorSystemErrorf("检查手机号唯一性失败").WithCause(err)
	}
	return exists, nil
}

// CountByTenantID 根据租户ID获取用户数量
func (r *userRepo) CountByTenantID(ctx context.Context, tenantID uint64) (int, error) {
	count, err := r.data.db.User.Query().Where(user.TenantIDEQ(tenantID)).Count(ctx)
	if err != nil {
		return 0, ec.ErrorSystemErrorf("获取租户用户数量失败").WithCause(err)
	}
	return count, nil
}
