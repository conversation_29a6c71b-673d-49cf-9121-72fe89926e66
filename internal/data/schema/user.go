package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.Int("age").Positive().Comment("年龄"),
		field.String("name").Default("unknown").Comment("名字"),
		field.Uint64("tenant_id").Comment("租户ID"),
		field.String("email").Optional().Unique().Comment("邮箱"),
		field.String("phone").Optional().Unique().Comment("手机号"),
		field.Int32("role").Default(1).Comment("用户角色：1-普通用户，2-管理员，3-超级管理员"),
		field.Int32("status").Default(1).Comment("用户状态：1-活跃，2-非活跃，3-暂停，4-已删除"),
		field.Uint64("creator_id").Optional().Comment("创建者ID"),
		field.Time("created_at").Comment("创建时间"),
		field.Time("updated_at").Comment("更新时间"),
		field.Time("last_login_time").Optional().Comment("最后登录时间"),
	}
}

// Edges of the User.
func (User) Edges() []ent.Edge {
	return nil
}

func (User) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "user"}}
}
