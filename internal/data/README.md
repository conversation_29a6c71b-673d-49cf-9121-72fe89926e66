# Data

## 目录结构

- `schema/` - 数据库模式定义文件（开发者维护）
  - `user.go` - 用户实体模式定义
  - `generate.go` - Ent 代码生成配置
- `data.go` - 数据层初始化和配置
- `user_repo.go` - 用户仓储实现

## 模式文件管理

数据库模式文件位于 `internal/data/schema/` 目录中，这些文件由开发者直接维护。
生成的 Ent 代码位于 `internal/gen/database/ent/` 目录中。

### 生成流程

1. 修改 `internal/data/schema/` 中的模式文件
2. 运行 `make ent` 生成代码
3. 生成的代码会出现在 `internal/gen/database/ent/` 目录中

### 注意事项

- 生成的代码位于 `internal/gen/` 目录下，受 Go internal 包保护
- 只需要维护 `internal/data/schema/` 中的文件
- 不要直接修改 `internal/gen/` 目录中的任何文件
